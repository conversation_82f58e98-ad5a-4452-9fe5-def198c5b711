#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/5 15:30
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : run_streamlit.py
# @Update  : 2025/8/5 15:30 Streamlit启动脚本

"""
Streamlit应用启动脚本

提供便捷的方式启动Streamlit数学教辅智能体应用，
包含环境检查、依赖验证和错误处理。
"""

import os
import sys
import subprocess
import logging
from pathlib import Path


def check_dependencies() -> bool:
    """检查必要的依赖是否已安装"""
    required_packages = [
        'streamlit',
        'langchain_openai',
        'langgraph',
        'PIL',
        'pix2tex'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装依赖:")
        print("uv sync  # 或 pip install -e .")
        return False
    
    print("✅ 所有依赖包已安装")
    return True


def check_environment() -> bool:
    """检查环境配置"""
    print("🔍 检查环境配置...")
    
    # 检查工作流文件是否存在
    workflow_file = Path("workflow/demo.py")
    if not workflow_file.exists():
        print(f"❌ 工作流文件不存在: {workflow_file}")
        return False
    
    # 检查Streamlit应用文件是否存在
    app_file = Path("streamlit_app.py")
    if not app_file.exists():
        print(f"❌ Streamlit应用文件不存在: {app_file}")
        return False
    
    print("✅ 环境配置检查通过")
    return True


def setup_logging() -> None:
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def main() -> None:
    """主函数"""
    print("🚀 启动AI数学教辅智能体 - Streamlit前端")
    print("=" * 50)
    
    # 设置日志
    setup_logging()
    
    # 检查环境
    if not check_environment():
        sys.exit(1)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 获取启动参数
    port = os.getenv('STREAMLIT_PORT', '8501')
    host = os.getenv('STREAMLIT_HOST', 'localhost')
    
    print(f"📡 启动服务器: http://{host}:{port}")
    print("💡 提示: 按 Ctrl+C 停止服务器")
    print("=" * 50)
    
    try:
        # 启动Streamlit应用
        cmd = [
            sys.executable, '-m', 'streamlit', 'run', 'streamlit_app.py',
            '--server.port', port,
            '--server.address', host,
            '--browser.gatherUsageStats', 'false'
        ]
        
        subprocess.run(cmd, check=True)
        
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
