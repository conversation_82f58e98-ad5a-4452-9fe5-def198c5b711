#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/2 15:16
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : main.py
# @Update  : 2025/8/2 15:16 更新描述
import asyncio
import logging

from config.logging_handler import configure_logging, logger
from vectorstore import EmbeddingManager
from vectorstore.sqlalchemy_store import VectorStore


async def main():
    """主函数"""
    # 配置统一日志系统
    configure_logging("langgraph_demo", level=logging.INFO)

    logger.info("启动应用...")

    try:
        vector_store = VectorStore(embedding_manager=EmbeddingManager())
        logger.info("向量存储初始化成功")

        res = await vector_store.get_document_count()
        logger.info(f"文档数量: {res}")

        await vector_store.add_texts(texts=["hello world"])
        res = await vector_store.get_document_count()
        logger.info(f"文档数量: {res}")
        logger.info(f"获取文档数量成功: {res}")

    except Exception as e:
        logger.error(f"应用运行失败: {str(e)}")
        logger.exception("详细错误信息:")
        return

    logger.info("应用运行完成")


if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main())
