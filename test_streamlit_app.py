#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/5 15:45
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : test_streamlit_app.py
# @Update  : 2025/8/5 15:45 Streamlit应用测试

"""
Streamlit应用测试脚本

测试Streamlit数学教辅智能体应用的核心功能，
包括界面组件、工作流集成和错误处理。
"""

import unittest
import sys
import os
from pathlib import Path

try:
    from unittest.mock import Mock, patch, MagicMock
except ImportError:
    # For Python < 3.3, use mock library
    try:
        from mock import Mock, patch, MagicMock
    except ImportError:
        print("警告: 无法导入mock模块，跳过需要mock的测试")
        Mock = patch = MagicMock = None

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

try:
    from streamlit_app import StreamlitMathTutorInterface
    from workflow.demo import detect_input_type, create_inputs_with_auto_detection
except ImportError as e:
    print("导入错误: " + str(e))
    print("请确保已安装所有依赖并且文件路径正确")
    sys.exit(1)


class TestStreamlitMathTutorInterface(unittest.TestCase):
    """测试Streamlit数学教辅智能体界面"""

    def setUp(self):
        """测试前设置"""
        # 跳过需要Streamlit的测试，如果没有mock
        if Mock is None:
            self.skipTest("Mock模块不可用")

    def test_basic_functionality(self):
        """测试基本功能"""
        # 简单的导入测试
        try:
            from streamlit_app import StreamlitMathTutorInterface
            self.assertTrue(True, "成功导入StreamlitMathTutorInterface")
        except ImportError as e:
            self.fail("无法导入StreamlitMathTutorInterface: " + str(e))

    def test_input_type_detection(self):
        """测试输入类型检测功能"""
        # 测试文本输入
        text_input = "求解方程 2x + 5 = 13"
        self.assertEqual(detect_input_type(text_input), "text")

        # 测试LaTeX输入
        latex_input = "\\frac{d}{dx}(x^2 + 3x + 2)"
        self.assertEqual(detect_input_type(latex_input), "latex")

        # 测试图片输入
        image_input = "test_image.png"
        self.assertEqual(detect_input_type(image_input), "image")

    def test_create_inputs_with_auto_detection(self):
        """测试自动输入创建功能"""
        content = "求解方程 3x - 7 = 8"
        profile = {"education": "初中", "age": 14}

        inputs = create_inputs_with_auto_detection(content, profile)

        self.assertEqual(inputs["input_content"], content)
        self.assertEqual(inputs["input_type"], "text")
        self.assertEqual(inputs["user_profile"], profile)
        self.assertEqual(inputs["retry_count"], 0)


class TestWorkflowIntegration(unittest.TestCase):
    """测试工作流集成"""

    def test_workflow_import(self):
        """测试工作流模块导入"""
        try:
            from workflow.demo import app, AgentState
            self.assertIsNotNone(app)
            self.assertIsNotNone(AgentState)
        except ImportError:
            self.fail("无法导入工作流模块")

    def test_workflow_execution_mock(self):
        """测试工作流执行（模拟）"""
        # 创建模拟输入
        inputs = {
            "input_content": "求解方程 2x + 5 = 13",
            "input_type": "text",
            "user_profile": {"education": "初中", "age": 14},
            "retry_count": 0,
        }
        
        # 验证输入格式
        self.assertIn("input_content", inputs)
        self.assertIn("input_type", inputs)
        self.assertIn("user_profile", inputs)
        self.assertIn("retry_count", inputs)
        
        # 验证用户画像格式
        profile = inputs["user_profile"]
        self.assertIn("education", profile)
        self.assertIn("age", profile)


class TestErrorHandling(unittest.TestCase):
    """测试错误处理"""

    def test_empty_input_handling(self):
        """测试空输入处理"""
        # 测试空字符串
        self.assertEqual(detect_input_type(""), "text")
        self.assertEqual(detect_input_type("   "), "text")

    def test_invalid_input_type(self):
        """测试无效输入类型处理"""
        # 这里可以添加更多的边界情况测试
        unusual_input = "!@#$%^&*()"
        result = detect_input_type(unusual_input)
        self.assertIn(result, ["text", "latex", "image"])


def run_tests():
    """运行所有测试"""
    print("🧪 开始运行Streamlit应用测试...")
    print("=" * 50)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestStreamlitMathTutorInterface,
        TestWorkflowIntegration,
        TestErrorHandling
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果
    print("=" * 50)
    if result.wasSuccessful():
        print("✅ 所有测试通过！")
        return True
    else:
        print("❌ 测试失败: " + str(len(result.failures)) + " 个失败, " + str(len(result.errors)) + " 个错误")
        return False


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
