# 向量存储 ORM 迁移指南

## 概述

本次更新将向量存储管理器从使用原生 SQL 查询重构为使用 SQLAlchemy ORM，并添加了 HNSW 向量索引以提升性能。同时实现了向量维度的配置化，提高了系统的灵活性和可维护性。

## 主要改进

### 1. ORM 替代原生 SQL

**之前的问题：**
- 使用原生 SQL 查询，代码难以维护
- 缺乏类型安全
- 手动处理参数绑定和类型转换
- 容易出现 SQL 注入风险

**现在的解决方案：**
- 完全使用 SQLAlchemy ORM 进行数据库操作
- 提供完整的类型安全
- 自动处理参数绑定和类型转换
- 更好的代码可读性和可维护性

### 2. HNSW 向量索引

**新增功能：**
- 自动创建 HNSW (Hierarchical Navigable Small World) 向量索引
- 使用余弦相似度操作符 (`vector_cosine_ops`)
- 优化的索引参数：`m=16, ef_construction=64`
- 显著提升向量相似度搜索性能

**索引配置：**
```python
Index(
    'idx_embedding_hnsw',
    'embedding',
    postgresql_using='hnsw',
    postgresql_with={'m': 16, 'ef_construction': 64},
    postgresql_ops={'embedding': 'vector_cosine_ops'}
)
```

### 3. 向量维度配置化

**配置方式：**
- 向量维度从 `config.yaml` 文件中的 `vector_store.vector_size` 获取
- 支持动态调整，无需修改代码
- 适配不同的嵌入模型

**配置文件示例：**
```yaml
vector_store:
  collection_name: knowledge_base
  vector_size: 1024  # 可配置的向量维度
  similarity_threshold: 0.7
  max_results: 5
```

## 代码变更对比

### 添加文档

**之前（原生 SQL）：**
```python
sql = f"""
    INSERT INTO vector_documents
    (content, embedding, source, document_type, created_at, updated_at)
    VALUES (:content, '{str(embedding)}'::vector(1024), :source, :document_type, :created_at, :updated_at)
    RETURNING id
"""
result = await session.execute(text(sql), params)
```

**现在（ORM）：**
```python
vector_doc = VectorDocument(
    content=doc.page_content,
    embedding=embedding,
    source=metadata.get("source", "unknown"),
    document_type=metadata.get("document_type", "text"),
    created_at=metadata.get("created_at", current_time),
    updated_at=metadata.get("updated_at", current_time),
)
session.add(vector_doc)
await session.flush()
```

### 相似度搜索

**之前（原生 SQL）：**
```python
stmt = text("""
    SELECT * FROM vector_documents
    ORDER BY embedding <=> :query_embedding::vector(1024)
    LIMIT :limit
""")
```

**现在（ORM）：**
```python
stmt = select(VectorDocument).order_by(
    VectorDocument.embedding.cosine_distance(query_embedding)
).limit(k)
```

## 性能优化

### 1. 向量索引性能

- **HNSW 索引**：提供近似最近邻搜索，时间复杂度为 O(log n)
- **余弦相似度**：使用 `vector_cosine_ops` 操作符优化
- **索引参数**：`m=16, ef_construction=64` 平衡了构建时间和搜索精度

### 2. 查询优化

- **ORM 查询优化**：SQLAlchemy 自动优化查询计划
- **批量操作**：支持批量插入和删除
- **连接池**：使用异步连接池提升并发性能

## 使用示例

### 基本使用

```python
from vectorstore.sqlalchemy_store import SQLAlchemyVectorStoreManager
from langchain_core.documents import Document

# 初始化向量存储管理器
vector_store = SQLAlchemyVectorStoreManager()
await vector_store.initialize()

# 添加文档
documents = [
    Document(
        page_content="这是一个关于人工智能的文档",
        metadata={"source": "ai_docs", "document_type": "article"}
    )
]
doc_ids = await vector_store.add_documents(documents)

# 相似度搜索
results = await vector_store.similarity_search("人工智能技术", k=5)

# 带分数的搜索
results_with_scores = await vector_store.similarity_search_with_score("人工智能技术", k=5)
```

### 配置化使用

```python
from config.config import config

# 获取配置的向量维度
vector_size = config.vector_store.vector_size
similarity_threshold = config.vector_store.similarity_threshold
max_results = config.vector_store.max_results

print(f"向量维度: {vector_size}")
print(f"相似度阈值: {similarity_threshold}")
print(f"最大结果数: {max_results}")
```

## 测试验证

### 运行测试

```bash
# 运行 ORM 功能测试
uv run python examples/test_vectorstore_orm.py

# 运行配置化演示
uv run python examples/vector_dimension_demo.py
```

### 测试覆盖

- ✅ 文档添加和检索
- ✅ 相似度搜索
- ✅ 带分数的搜索
- ✅ 过滤搜索
- ✅ 文档更新和删除
- ✅ 向量索引创建
- ✅ 配置化功能

## 迁移注意事项

### 1. 数据库迁移

如果从旧版本升级，需要：

1. 确保 pgvector 扩展已安装
2. 运行数据库迁移以创建新的索引
3. 验证向量维度配置正确

### 2. 配置更新

更新 `config.yaml` 文件：

```yaml
vector_store:
  vector_size: 1024  # 根据您的嵌入模型调整
  similarity_threshold: 0.7
  max_results: 5
```

### 3. 代码兼容性

- 保持了原有的 API 接口
- 提供了 `VectorStore` 别名以保持兼容性
- 所有现有代码无需修改即可使用

## 最佳实践

### 1. 向量维度配置

- 根据嵌入模型的实际输出维度配置 `vector_size`
- 常见维度：1024 (qwen3:0.6b), 1536 (text-embedding-3-small), 4096 (text-embedding-3-large)

### 2. 索引优化

- HNSW 索引适合高维向量和频繁的相似度搜索
- 对于大规模数据，考虑调整 `m` 和 `ef_construction` 参数

### 3. 查询优化

- 使用过滤条件减少搜索空间
- 合理设置 `k` 参数避免返回过多结果
- 利用相似度阈值过滤低质量结果

## 总结

本次重构显著提升了向量存储系统的：

- **性能**：HNSW 索引提供高效的向量搜索
- **可维护性**：ORM 提供更好的代码结构和类型安全
- **灵活性**：配置化支持不同模型和环境
- **可靠性**：自动索引创建和错误处理

通过这些改进，系统现在能够更好地支持大规模向量搜索应用，同时保持代码的简洁性和可维护性。
