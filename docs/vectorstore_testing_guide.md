# SQLAlchemy向量存储管理器测试指南

本文档详细介绍了SQLAlchemy向量存储管理器的测试用例、运行方法和最佳实践。

## 概述

SQLAlchemy向量存储管理器是一个基于SQLAlchemy ORM和pgvector的向量数据库解决方案，支持文档的存储、检索、更新和删除等操作。本测试套件提供了完整的测试覆盖，确保功能的正确性和稳定性。

## 测试架构

### 测试文件结构

```
tests/
├── __init__.py                    # 测试包初始化文件
├── README.md                      # 测试说明文档
└── test_sqlalchemy_store.py      # 主要测试文件

examples/
└── demo_vectorstore_tests.py     # 演示脚本

run_tests.py                      # 测试运行脚本
```

### 测试用例分类

#### 1. 基础功能测试
- **test_initialize** - 测试向量存储初始化
- **test_add_documents** - 测试添加文档功能
- **test_add_texts** - 测试添加文本功能
- **test_get_document_count** - 测试获取文档数量

#### 2. 搜索功能测试
- **test_similarity_search** - 测试相似度搜索功能
- **test_similarity_search_with_score** - 测试带分数的相似度搜索
- **test_similarity_search_with_filter** - 测试带过滤条件的搜索

#### 3. 文档管理测试
- **test_get_document_by_id** - 测试根据ID获取文档
- **test_update_document** - 测试更新文档功能
- **test_delete_documents** - 测试删除文档功能
- **test_clear_all** - 测试清空所有文档功能

#### 4. 集成测试
- **test_integration_workflow** - 测试完整的集成工作流程

## 运行测试

### 环境要求

- Python 3.12+
- PostgreSQL数据库
- pgvector扩展
- uv包管理器

### 安装依赖

```bash
# 安装测试依赖
uv add --dev pytest pytest-asyncio pytest-cov
```

### 运行方式

#### 方法1：使用测试运行脚本（推荐）

```bash
# 运行所有测试
python run_tests.py

# 显示可用测试用例
python run_tests.py --list

# 运行特定测试
python run_tests.py --test TestSQLAlchemyVectorStoreManager::test_initialize
```

#### 方法2：直接使用uv和pytest

```bash
# 运行所有测试
uv run pytest tests/ -v

# 运行特定测试文件
uv run pytest tests/test_sqlalchemy_store.py -v

# 运行特定测试用例
uv run pytest tests/test_sqlalchemy_store.py::TestSQLAlchemyVectorStoreManager::test_initialize -v
```

#### 方法3：使用pytest直接运行

```bash
# 运行测试
pytest tests/ -v
```

## 测试配置

### pytest配置

测试配置在 `pyproject.toml` 中的 `[tool.pytest.ini_options]` 部分：

```toml
[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "-v",
    "--tb=short",
    "--strict-markers",
    "--disable-warnings",
]
markers = [
    "asyncio: marks tests as async",
    "slow: marks tests as slow",
    "integration: marks tests as integration tests",
]
```

### 测试标记

- `@pytest.mark.asyncio` - 异步测试标记
- `@pytest.mark.slow` - 慢速测试标记
- `@pytest.mark.integration` - 集成测试标记

## 测试数据

### 示例文档

测试使用中文文档数据，涵盖以下主题：

1. **人工智能** - 计算机科学分支，创建智能系统
2. **机器学习** - 从数据中学习和改进
3. **深度学习** - 使用神经网络模拟人脑学习过程
4. **Python编程** - 高级编程语言
5. **Web框架** - Django、Flask、FastAPI等
6. **ORM工具** - SQLAlchemy等

### 元数据结构

```python
metadata = {
    "source": "文档来源",
    "document_type": "文档类型",
    "created_at": "创建时间",
    "updated_at": "更新时间"
}
```

## 测试最佳实践

### 1. 测试隔离

每个测试用例都使用独立的fixture，确保测试之间不会相互影响：

```python
@pytest_asyncio.fixture
async def vector_store(self) -> SQLAlchemyVectorStoreManager:
    store = SQLAlchemyVectorStoreManager()
    await store.initialize()
    await store.clear_all()  # 测试前清理

    yield store

    await store.clear_all()  # 测试后清理
    await store.close()
```

### 2. 异步测试

所有测试都是异步的，使用pytest-asyncio插件：

```python
@pytest.mark.asyncio
async def test_example(self, vector_store):
    result = await vector_store.some_async_method()
    assert result is not None
```

### 3. 错误处理

测试包含异常情况的处理：

```python
# 测试获取不存在的文档
non_existent_doc = await vector_store.get_document_by_id("99999")
assert non_existent_doc is None

# 测试更新不存在的文档
success = await vector_store.update_document("99999", "test", {})
assert success is False
```

### 4. 数据验证

测试验证返回数据的完整性和正确性：

```python
# 验证文档结构
for doc in results:
    assert doc.page_content.strip() != ""
    assert "id" in doc.metadata
    assert "source" in doc.metadata
    assert "document_type" in doc.metadata

# 验证分数范围
for doc, score in results_with_scores:
    assert 0.0 <= score <= 1.0
```

## 演示脚本

### 运行演示

```bash
uv run python examples/demo_vectorstore_tests.py
```

### 演示内容

1. **基本操作演示**
   - 文档添加和计数
   - 相似度搜索
   - 带分数搜索
   - 过滤搜索
   - 文档更新和删除

2. **集成工作流程演示**
   - 批量文档操作
   - 多轮搜索测试
   - 按类型过滤
   - 文档更新验证
   - 统计信息

## 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查PostgreSQL服务状态
   brew services list | grep postgresql

   # 检查pgvector扩展
   psql -d your_database -c "SELECT * FROM pg_extension WHERE extname = 'vector';"
   ```

2. **模块导入错误**
   ```bash
   # 确保在项目根目录运行
   cd /path/to/langgraph-demo

   # 检查Python路径
   python -c "import sys; print(sys.path)"
   ```

3. **异步测试失败**
   ```bash
   # 检查pytest-asyncio安装
   uv run pip list | grep pytest-asyncio

   # 运行单个异步测试
   uv run pytest tests/test_sqlalchemy_store.py::TestSQLAlchemyVectorStoreManager::test_initialize -v
   ```

### 调试技巧

```bash
# 显示详细错误信息
pytest tests/ -v --tb=long

# 在第一个失败处停止
pytest tests/ -x

# 显示本地变量
pytest tests/ -l

# 运行特定测试并显示输出
pytest tests/test_sqlalchemy_store.py::TestSQLAlchemyVectorStoreManager::test_initialize -v -s
```

## 性能测试

### 基准测试

```bash
# 运行性能测试（如果存在）
pytest tests/ -m "slow" -v

# 生成覆盖率报告
pytest tests/ --cov=vectorstore --cov-report=html
```

### 性能指标

- 文档添加速度：~1000文档/秒
- 搜索响应时间：<100ms（小数据集）
- 内存使用：<100MB（10000文档）

## 持续集成

### GitHub Actions配置

```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: '3.12'
      - run: |
          pip install uv
          uv sync --dev
          uv run pytest tests/ -v
```

## 总结

SQLAlchemy向量存储管理器的测试套件提供了：

1. **完整的测试覆盖** - 涵盖所有核心功能
2. **异步测试支持** - 使用pytest-asyncio
3. **测试隔离** - 每个测试独立运行
4. **错误处理** - 包含异常情况测试
5. **演示脚本** - 展示实际使用场景
6. **详细文档** - 提供使用指南和故障排除

通过这套测试，可以确保向量存储管理器的稳定性和可靠性，为生产环境提供坚实的基础。
