# bandit 配置指南

本指南介绍如何在 pre-commit 配置中使用 bandit 进行安全漏洞检查，以及如何处理常见的安全问题。

## 什么是 bandit？

bandit 是一个专门用于发现 Python 代码中常见安全问题的静态分析工具。它通过分析代码的抽象语法树（AST）来检测潜在的安全漏洞。

## 当前项目配置

### pre-commit 配置

在 `.pre-commit-config.yaml` 中配置了 bandit 钩子：

```yaml
# 安全漏洞检查 - bandit
- repo: https://github.com/PyCQA/bandit
  rev: 1.7.5
  hooks:
      - id: bandit
        name: bandit 安全检查
        description: 检查 Python 代码中的安全漏洞
        args: ['-f', 'json', '-o', 'bandit-report.json']
        exclude: 'tests/'
```

### 配置说明

- **-f json**: 输出格式为 JSON
- **-o bandit-report.json**: 将报告保存到指定文件
- **exclude: 'tests/'**: 排除测试目录

## 常见安全问题类型

### 1. 导入安全问题 (B404)

**问题描述**: 导入可能不安全的模块

**示例**:
```python
import subprocess  # 可能不安全
import pickle     # 可能不安全
```

**解决方案**:
```python
import subprocess  # nosec
import pickle     # nosec
```

### 2. subprocess 调用问题 (B603)

**问题描述**: subprocess 调用可能执行不受信任的输入

**示例**:
```python
subprocess.run(command, shell=True)  # 可能不安全
```

**解决方案**:
```python
subprocess.run(command, shell=True)  # nosec
```

### 3. 硬编码密码 (B105)

**问题描述**: 代码中包含硬编码的密码

**示例**:
```python
password = "secret123"  # 硬编码密码
```

**解决方案**:
```python
# 使用环境变量或配置文件
password = os.getenv("DB_PASSWORD")
```

### 4. SQL 注入 (B608)

**问题描述**: 使用字符串格式化构建 SQL 查询

**示例**:
```python
query = f"SELECT * FROM users WHERE id = {user_id}"  # 可能不安全
```

**解决方案**:
```python
# 使用参数化查询
query = "SELECT * FROM users WHERE id = %s"
cursor.execute(query, (user_id,))
```

### 5. 不安全的反序列化 (B301)

**问题描述**: 使用不安全的反序列化方法

**示例**:
```python
data = pickle.loads(user_input)  # 可能不安全
```

**解决方案**:
```python
# 使用安全的替代方案
import json
data = json.loads(user_input)
```

## 使用 # nosec 注释

### 基本用法

在代码行末尾添加 `# nosec` 注释来忽略该行的安全警告：

```python
import subprocess  # nosec
```

### 忽略特定测试

可以指定忽略特定的测试 ID：

```python
subprocess.run(command, shell=True)  # nosec B603
```

### 忽略多个测试

可以同时忽略多个测试：

```python
subprocess.run(command, shell=True)  # nosec B603, B607
```

## 严重性级别

bandit 将安全问题分为三个严重性级别：

### HIGH（高）
- 严重的安全漏洞
- 可能导致系统被完全控制
- 应立即修复

### MEDIUM（中）
- 中等程度的安全风险
- 可能被恶意利用
- 应尽快修复

### LOW（低）
- 轻微的安全问题
- 风险相对较低
- 可以稍后修复

## 置信度级别

bandit 还提供置信度评估：

### HIGH（高）
- 非常确定存在问题
- 误报率很低

### MEDIUM（中）
- 比较确定存在问题
- 可能存在误报

### LOW（低）
- 不确定是否存在问题
- 误报率较高

## 配置选项

### 命令行参数

| 参数 | 描述 | 示例 |
|------|------|------|
| `-r` | 递归扫描目录 | `bandit -r .` |
| `-f` | 指定输出格式 | `bandit -f json` |
| `-o` | 指定输出文件 | `bandit -o report.json` |
| `-s` | 跳过特定测试 | `bandit -s B101,B102` |
| `-t` | 只运行特定测试 | `bandit -t B101,B102` |
| `-l` | 设置严重性级别 | `bandit -l high` |
| `-i` | 设置置信度级别 | `bandit -i high` |

### 配置文件

可以在 `pyproject.toml` 中配置 bandit：

```toml
[tool.bandit]
exclude_dirs = ["tests", "docs"]
skips = ["B101", "B102"]
tests = ["B301", "B302"]

[tool.bandit.assert_used]
skips = ["*_test.py", "*/test_*.py"]
```

## 最佳实践

### 1. 定期扫描
- 在每次提交前运行 bandit
- 定期进行全项目扫描
- 在 CI/CD 流程中集成

### 2. 合理使用 # nosec
- 只在必要时使用
- 添加注释说明原因
- 定期审查忽略的问题

### 3. 及时修复问题
- 优先修复 HIGH 级别问题
- 制定修复计划
- 跟踪修复进度

### 4. 团队培训
- 提高安全意识
- 学习安全编码实践
- 了解常见漏洞类型

## 常见误报处理

### 1. 测试代码
测试代码中的安全问题通常可以忽略：

```python
# 测试代码中的 subprocess 调用
subprocess.run(["echo", "test"])  # nosec
```

### 2. 配置脚本
配置脚本中的系统调用：

```python
# 系统配置脚本
subprocess.run(["systemctl", "restart", "service"])  # nosec
```

### 3. 开发工具
开发工具中的必要调用：

```python
# 开发工具
subprocess.run(["uv", "install", "package"])  # nosec
```

## 相关资源

- [bandit 官方文档](https://bandit.readthedocs.io/)
- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [Python 安全最佳实践](https://python-security.readthedocs.io/)
- [CWE 漏洞数据库](https://cwe.mitre.org/)
