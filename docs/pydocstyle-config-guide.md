# pydocstyle 配置指南

本指南介绍如何在 pre-commit 配置中使用 pydocstyle 进行文档字符串检查，以及如何处理常见的错误代码。

## 什么是 pydocstyle？

pydocstyle 是一个用于检查 Python 文档字符串是否符合 PEP 257 规范的静态分析工具。它可以帮助确保代码文档的一致性和质量。

## 当前项目配置

### pre-commit 配置

在 `.pre-commit-config.yaml` 中配置了 pydocstyle 钩子：

```yaml
# 文档字符串检查 - pydocstyle
- repo: https://github.com/pycqa/pydocstyle
  rev: 6.3.0
  hooks:
      - id: pydocstyle
        name: pydocstyle 文档检查
        description: 检查 Python 文档字符串的格式和内容
        args: ['--convention=google', '--add-ignore=D100,D104,D106,D200,D205,D400,D401,D403,D415,D212,D103,D107,D202']
```

### 配置说明

- **--convention=google**: 使用 Google 风格的文档字符串格式
- **--add-ignore**: 忽略特定的错误代码

## 错误代码说明

### 已忽略的错误代码

| 错误代码 | 描述 | 忽略原因 |
|---------|------|----------|
| D100 | Missing docstring in public module | 模块级别的文档字符串不是必需的 |
| D103 | Missing docstring in public function | 某些函数（如测试函数）不需要文档字符串 |
| D104 | Missing docstring in public package | 包级别的文档字符串不是必需的 |
| D106 | Missing docstring in public nested class | 嵌套类的文档字符串不是必需的 |
| D107 | Missing docstring in __init__ | __init__ 方法通常不需要文档字符串 |
| D200 | One-line docstring should fit on one line | 允许多行文档字符串 |
| D202 | No blank lines allowed after function docstring | 允许文档字符串后的空行 |
| D205 | 1 blank line required between summary line and description | 允许更灵活的格式 |
| D212 | Multi-line docstring summary should start at the first line | 允许多行摘要 |
| D400 | First line should end with a period | 允许不以句号结尾 |
| D401 | First line should be in imperative mood | 允许更自然的语言 |
| D403 | First word of the first line should be properly capitalized | 允许小写开头 |
| D415 | First line should end with a period, question mark, or exclamation point | 允许不以标点结尾 |

### 常见错误代码

| 错误代码 | 描述 | 示例 |
|---------|------|------|
| D100 | Missing docstring in public module | 模块缺少文档字符串 |
| D101 | Missing docstring in public class | 类缺少文档字符串 |
| D102 | Missing docstring in public method | 方法缺少文档字符串 |
| D103 | Missing docstring in public function | 函数缺少文档字符串 |
| D104 | Missing docstring in public package | 包缺少文档字符串 |
| D105 | Missing docstring in magic method | 魔术方法缺少文档字符串 |
| D106 | Missing docstring in public nested class | 嵌套类缺少文档字符串 |
| D107 | Missing docstring in __init__ | __init__ 方法缺少文档字符串 |
| D200 | One-line docstring should fit on one line | 单行文档字符串应该在一行内 |
| D201 | No blank lines allowed before function docstring | 函数文档字符串前不允许空行 |
| D202 | No blank lines allowed after function docstring | 函数文档字符串后不允许空行 |
| D203 | 1 blank line required before class docstring | 类文档字符串前需要空行 |
| D204 | 1 blank line required after class docstring | 类文档字符串后需要空行 |
| D205 | 1 blank line required between summary line and description | 摘要行和描述之间需要空行 |
| D206 | Docstring should be indented with spaces, not tabs | 文档字符串应该用空格缩进 |
| D207 | Docstring is under-indented | 文档字符串缩进不足 |
| D208 | Docstring is over-indented | 文档字符串缩进过多 |
| D209 | Multi-line docstring closing quotes should be on a separate line | 多行文档字符串的结束引号应该在单独的行 |
| D210 | No whitespaces allowed surrounding docstring text | 文档字符串文本周围不允许空白 |
| D211 | No blank lines allowed before class docstring | 类文档字符串前不允许空行 |
| D212 | Multi-line docstring summary should start at the first line | 多行文档字符串摘要应该从第一行开始 |
| D213 | Multi-line docstring summary should start at the second line | 多行文档字符串摘要应该从第二行开始 |
| D214 | Section is over-indented | 章节缩进过多 |
| D215 | Section underline is over-indented | 章节下划线缩进过多 |
| D300 | Use """triple double quotes""" | 使用三重双引号 |
| D301 | Use r""" if any backslashes in a docstring""" | 如果文档字符串中有反斜杠，使用 r""" |
| D400 | First line should end with a period | 第一行应该以句号结尾 |
| D401 | First line should be in imperative mood | 第一行应该使用祈使语气 |
| D402 | First line should not be the function's "signature" | 第一行不应该是函数的"签名" |
| D403 | First word of the first line should be properly capitalized | 第一行的第一个词应该正确大写 |
| D404 | First word of the docstring should not be "This" | 文档字符串的第一个词不应该是"This" |
| D405 | Section name should be properly capitalized | 章节名称应该正确大写 |
| D406 | Section name should end with a newline | 章节名称应该以换行符结尾 |
| D407 | Missing dashed underline after section | 章节后缺少虚线 |
| D408 | Section underline should be in the line following the section's name | 章节下划线应该在章节名称的下一行 |
| D409 | Section underline should match the length of its name | 章节下划线应该与其名称长度匹配 |
| D410 | Missing blank line after section | 章节后缺少空行 |
| D411 | Missing blank line before section | 章节前缺少空行 |
| D412 | No blank lines allowed between a section header and its content | 章节标题和内容之间不允许空行 |
| D413 | Missing blank line after last section | 最后一个章节后缺少空行 |
| D414 | Section has no content | 章节没有内容 |
| D415 | First line should end with a period, question mark, or exclamation point | 第一行应该以句号、问号或感叹号结尾 |
| D416 | Section name should end with a colon | 章节名称应该以冒号结尾 |
| D417 | Missing argument descriptions in the docstring | 文档字符串中缺少参数描述 |

## Google 风格文档字符串格式

### 函数文档字符串

```python
def function_name(param1: str, param2: int) -> bool:
    """函数简短描述。

    函数的详细描述，可以跨多行。

    Args:
        param1: 参数1的描述
        param2: 参数2的描述

    Returns:
        返回值的描述

    Raises:
        ValueError: 当参数无效时抛出
        TypeError: 当参数类型错误时抛出

    Example:
        >>> function_name("test", 42)
        True
    """
    pass
```

### 类文档字符串

```python
class MyClass:
    """类的简短描述。

    类的详细描述，可以跨多行。

    Attributes:
        attr1: 属性1的描述
        attr2: 属性2的描述

    Example:
        >>> obj = MyClass()
        >>> obj.method()
    """

    def __init__(self, param: str):
        """初始化方法。

        Args:
            param: 初始化参数
        """
        self.attr1 = param
```

### 模块文档字符串

```python
"""模块的简短描述。

模块的详细描述，可以跨多行。

Example:
    >>> import mymodule
    >>> mymodule.function()
"""

# 模块内容...
```

## 最佳实践

1. **保持简洁**: 文档字符串应该简洁明了
2. **使用祈使语气**: 使用"Return"而不是"Returns"
3. **描述参数和返回值**: 为所有参数和返回值提供描述
4. **提供示例**: 为复杂函数提供使用示例
5. **保持一致性**: 在整个项目中保持文档字符串格式一致

## 常见问题解决

### 1. 忽略特定错误

在 pre-commit 配置中添加错误代码到 `--add-ignore` 参数：

```yaml
args: ['--convention=google', '--add-ignore=D100,D103,D107']
```

### 2. 使用内联忽略

在特定行使用内联注释忽略错误：

```python
def function_without_docstring():  # noqa: D103
    pass
```

### 3. 配置 pyproject.toml

在 `pyproject.toml` 中配置 pydocstyle：

```toml
[tool.pydocstyle]
convention = "google"
add-ignore = ["D100", "D103", "D107"]
```

## 相关资源

- [pydocstyle 官方文档](https://www.pydocstyle.org/)
- [PEP 257 - Docstring Conventions](https://www.python.org/dev/peps/pep-0257/)
- [Google Python Style Guide](https://google.github.io/styleguide/pyguide.html#38-comments-and-docstrings)
