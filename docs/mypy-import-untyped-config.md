# mypy import-untyped 配置指南

本指南介绍如何在 pre-commit 配置中忽略 mypy 的 `import-untyped` 检查。

## 问题描述

`import-untyped` 是 mypy 的一个错误代码，当导入的模块没有类型注解或类型存根时会触发此错误。这在以下情况下很常见：

- 导入第三方库但没有安装对应的类型存根包
- 导入的模块没有 `py.typed` 标记
- 导入的模块缺少类型注解

## 解决方案

### 1. 在 pyproject.toml 中全局禁用（推荐）

在 `pyproject.toml` 文件的 `[tool.mypy]` 部分添加：

```toml
[tool.mypy]
# 其他配置...
disable_error_code = ["import-untyped"]
```

### 2. 在 pre-commit 配置中禁用

在 `.pre-commit-config.yaml` 文件的 mypy 钩子中添加参数：

```yaml
- repo: https://github.com/pre-commit/mirrors-mypy
  rev: v1.8.0
  hooks:
    - id: mypy
      name: mypy 类型检查
      description: 检查 Python 代码的类型注解
      args: [
        '--disable-error-code=import-untyped'
      ]
```

### 3. 针对特定模块禁用

在 `pyproject.toml` 中使用模块覆盖：

```toml
[[tool.mypy.overrides]]
module = "config.*"
disable_error_code = ["import-untyped"]
```

### 4. 使用内联注释禁用

在特定的导入语句后添加注释：

```python
import some_untyped_module  # type: ignore[import-untyped]
```

## 当前项目配置

本项目采用了以下配置策略：

### 全局配置

```toml
[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_unreachable = true
strict_equality = true
check_untyped_defs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
ignore_missing_imports = true
# 禁用 import-untyped 错误代码
disable_error_code = ["import-untyped"]
```

### 模块特定配置

```toml
# 测试模块 - 允许无类型定义
[[tool.mypy.overrides]]
module = "tests.*"
allow_untyped_defs = true
allow_untyped_calls = true
disable_error_code = ["no-untyped-def", "misc", "no-any-return", "union-attr"]

# 示例模块 - 允许无类型定义
[[tool.mypy.overrides]]
module = "examples.*"
allow_untyped_defs = true
allow_untyped_calls = true
disable_error_code = ["no-untyped-def", "misc", "no-any-return", "union-attr"]

# 脚本模块 - 允许无类型定义
[[tool.mypy.overrides]]
module = "scripts.*"
allow_untyped_defs = true
allow_untyped_calls = true
disable_error_code = ["no-untyped-def", "misc", "no-any-return", "union-attr", "attr-defined"]

# Alembic 模块 - 忽略缺失导入
[[tool.mypy.overrides]]
module = "alembic.*"
ignore_missing_imports = true
disable_error_code = ["attr-defined", "no-untyped-def", "misc", "no-any-return", "union-attr"]

# 数据库模块 - 忽略缺失导入
[[tool.mypy.overrides]]
module = "database.*"
ignore_missing_imports = true
disable_error_code = ["attr-defined", "no-untyped-def", "misc", "no-any-return", "union-attr"]

# 配置模块 - 忽略缺失导入
[[tool.mypy.overrides]]
module = "config.*"
ignore_missing_imports = true
disable_error_code = ["import-untyped", "no-untyped-def", "misc", "no-any-return", "union-attr", "attr-defined"]

# 向量存储模块 - 忽略缺失导入
[[tool.mypy.overrides]]
module = "vectorstore.*"
ignore_missing_imports = true
disable_error_code = ["import-untyped", "no-untyped-def", "misc", "no-any-return", "union-attr", "attr-defined"]

# 主模块 - 忽略缺失导入
[[tool.mypy.overrides]]
module = "main"
ignore_missing_imports = true
disable_error_code = ["import-untyped", "no-untyped-def", "misc", "no-any-return", "union-attr", "attr-defined"]
```

## 其他相关错误代码

除了 `import-untyped`，mypy 还有其他常见的错误代码：

- `no-untyped-def`: 函数缺少类型注解
- `misc`: 杂项错误
- `no-any-return`: 函数返回 Any 类型
- `union-attr`: 联合类型的属性访问错误
- `attr-defined`: 属性未定义错误
- `annotation-unchecked`: 注解未检查

## 最佳实践

1. **渐进式采用**: 不要一次性禁用所有错误，而是逐步修复代码
2. **模块化配置**: 为不同类型的模块设置不同的配置
3. **文档化**: 记录为什么禁用某些错误代码
4. **定期审查**: 定期检查是否可以重新启用某些检查

## 安装类型存根

对于第三方库，可以安装对应的类型存根包：

```bash
# 安装 PyYAML 的类型存根
uv add --group dev types-PyYAML

# 安装 requests 的类型存根
uv add --group dev types-requests

# 自动安装所有缺失的类型存根
mypy --install-types
```

## 相关资源

- [mypy 官方文档](https://mypy.readthedocs.io/)
- [mypy 错误代码列表](https://mypy.readthedocs.io/en/stable/error_code_list.html)
- [pre-commit 文档](https://pre-commit.com/)
