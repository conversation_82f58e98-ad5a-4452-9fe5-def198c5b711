# pre-commit 使用指南

本指南介绍如何在 langgraph-demo 项目中使用 pre-commit 进行代码格式化和质量检查。

## 什么是 pre-commit？

pre-commit 是一个用于管理和维护多语言预提交钩子的框架。它可以在代码提交前自动运行各种检查，确保代码质量和一致性。

## 功能特性

本项目配置的 pre-commit 钩子包括：

### 🔧 基础代码质量检查
- **check-yaml**: 检查 YAML 文件语法
- **check-json**: 检查 JSON 文件语法
- **check-merge-conflict**: 检查合并冲突标记
- **end-of-file-fixer**: 修复文件末尾换行符
- **trailing-whitespace**: 修复尾随空格
- **check-added-large-files**: 检查大文件（限制 500KB）
- **check-executables-have-shebangs**: 检查可执行文件的 shebang
- **detect-private-key**: 检测私钥文件

### 🐍 Python 代码格式化
- **black**: 自动格式化 Python 代码（行长度 88 字符）
- **isort**: 自动排序和格式化 Python 导入语句

### 🔍 Python 代码质量检查
- **flake8**: 检查 Python 代码风格和潜在问题
- **mypy**: 检查 Python 代码的类型注解
- **bandit**: 检查 Python 代码中的安全漏洞
- **pydocstyle**: 检查 Python 文档字符串格式

### 🎯 项目特定检查
- **check-version-update**: 检查版本号一致性
- **check-chinese-comments**: 检查中文注释格式
- **run-tests**: 运行测试套件（仅在推送前）

## 安装和设置

### 1. 快速安装

使用提供的设置脚本进行一键安装：

```bash
# 安装所有依赖和钩子
python scripts/setup_precommit.py all
```

### 2. 手动安装

如果需要手动安装，请按以下步骤操作：

```bash
# 1. 安装开发依赖
uv add --group dev pre-commit

# 2. 安装 pre-commit 钩子
uv run pre-commit install --hook-type pre-commit --hook-type pre-push

# 3. 更新钩子到最新版本
uv run pre-commit autoupdate

# 4. 在所有文件上运行检查
uv run pre-commit run --all-files
```

## 使用方法

### 自动运行

安装钩子后，pre-commit 会在以下情况下自动运行：

- **git commit**: 提交代码时运行 pre-commit 钩子
- **git push**: 推送代码时运行 pre-push 钩子

### 手动运行

```bash
# 在所有文件上运行所有钩子
uv run pre-commit run --all-files

# 运行特定的钩子
uv run pre-commit run black
uv run pre-commit run flake8

# 运行特定文件
uv run pre-commit run --files path/to/file.py

# 跳过钩子（不推荐）
git commit --no-verify
```

### 管理钩子

```bash
# 更新钩子到最新版本
uv run pre-commit autoupdate

# 清理钩子缓存
uv run pre-commit clean

# 卸载钩子
uv run pre-commit uninstall
```

## 配置说明

### 全局配置

- **exclude**: 排除的文件模式
- **fail_fast**: 是否在第一个失败时停止
- **minimum_pre_commit_version**: 最低 pre-commit 版本要求
- **default_install_hook_types**: 默认安装的钩子类型
- **default_language_version**: 默认语言版本

### 钩子配置

每个钩子可以配置以下参数：

- **id**: 钩子标识符
- **name**: 钩子显示名称
- **description**: 钩子描述
- **args**: 传递给钩子的参数
- **files**: 文件匹配模式
- **exclude**: 排除的文件模式
- **types**: 文件类型过滤
- **stages**: 运行阶段（pre-commit, pre-push 等）

## 自定义配置

### 添加新的钩子

在 `.pre-commit-config.yaml` 中添加新的钩子：

```yaml
- repo: https://github.com/example/hook-repo
  rev: v1.0.0
  hooks:
    - id: my-hook
      name: 我的钩子
      description: 自定义钩子描述
```

### 创建本地钩子

在 `repos` 列表中添加本地钩子：

```yaml
- repo: local
  hooks:
    - id: my-local-hook
      name: 本地钩子
      entry: python scripts/my_hook.py
      language: python
      types: [python]
```

### 修改钩子参数

```yaml
- id: black
  args: ['--line-length=100']  # 修改行长度
```

## 故障排除

### 常见问题

1. **钩子运行失败**
   ```bash
   # 查看详细错误信息
   uv run pre-commit run --verbose
   ```

2. **钩子运行缓慢**
   ```bash
   # 清理缓存
   uv run pre-commit clean
   ```

3. **特定文件被排除**
   - 检查 `.pre-commit-config.yaml` 中的 `exclude` 配置
   - 检查各个钩子的 `files` 和 `exclude` 配置

4. **版本冲突**
   ```bash
   # 更新到最新版本
   uv run pre-commit autoupdate
   ```

### 调试技巧

```bash
# 运行单个钩子并查看详细输出
uv run pre-commit run black --verbose

# 跳过钩子进行调试
git commit --no-verify

# 查看钩子配置
uv run pre-commit run --show-diff-on-failure
```

## 最佳实践

1. **定期更新钩子**: 使用 `uv run pre-commit autoupdate` 保持钩子最新
2. **在 CI/CD 中运行**: 在持续集成中运行 pre-commit 检查
3. **团队协作**: 确保团队成员都安装了相同的钩子
4. **文档更新**: 修改钩子配置时更新相关文档

## 相关文件

- `.pre-commit-config.yaml`: pre-commit 配置文件
- `scripts/setup_precommit.py`: 设置脚本
- `scripts/check_version.py`: 版本检查脚本
- `scripts/check_chinese_comments.py`: 中文注释检查脚本
- `pyproject.toml`: 项目依赖配置

## 更多资源

- [pre-commit 官方文档](https://pre-commit.com/)
- [Black 格式化工具](https://black.readthedocs.io/)
- [isort 导入排序](https://pycqa.github.io/isort/)
- [flake8 代码检查](https://flake8.pycqa.org/)
- [mypy 类型检查](https://mypy.readthedocs.io/)
