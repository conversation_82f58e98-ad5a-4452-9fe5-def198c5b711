# 🧮 AI数学教辅智能体 - Streamlit前端

基于LangGraph构建的智能数学教辅系统的Streamlit前端界面，提供直观易用的Web界面来与数学问题解答工作流进行交互。

## ✨ 功能特性

### 🎯 核心功能
- **多种输入方式**: 支持文本、图片和LaTeX公式输入
- **智能类型检测**: 自动识别输入内容类型
- **个性化配置**: 根据教育水平和年龄调整解答难度
- **实时进度显示**: 可视化工作流执行步骤
- **质量保证**: 内置审计机制确保答案准确性
- **历史记录**: 保存和查看解答历史

### 🔧 技术特点
- **响应式设计**: 适配不同屏幕尺寸
- **流式处理**: 实时显示工作流执行进度
- **错误处理**: 友好的错误提示和异常处理
- **会话管理**: 智能的状态管理和历史记录

## 🚀 快速开始

### 1. 安装依赖

确保您已经安装了项目依赖：

```bash
# 使用uv安装依赖（推荐）
uv sync

# 或使用pip安装
pip install -e .
```

### 2. 启动应用

```bash
# 启动Streamlit应用
streamlit run streamlit_app.py

# 或指定端口
streamlit run streamlit_app.py --server.port 8501
```

### 3. 访问界面

打开浏览器访问：`http://localhost:8501`

## 📖 使用指南

### 输入配置

#### 侧边栏设置
- **教育水平**: 选择学生的教育水平（小学/初中/高中/大学）
- **年龄**: 设置学生年龄（6-25岁）
- **输入模式**: 选择自动检测或手动指定输入类型

#### 输入方式

1. **文本输入**
   ```
   求解方程 2x + 5 = 13
   计算函数 f(x) = x² + 3x + 2 的导数
   解不等式 3x - 7 > 8
   ```

2. **图片上传**
   - 支持格式：PNG, JPG, JPEG, GIF, BMP, TIFF, WEBP
   - 建议分辨率：至少300x300像素
   - 文件大小：建议小于10MB

3. **LaTeX公式**
   ```latex
   \frac{d}{dx}(x^2 + 3x + 2)
   \int x^2 dx
   \lim_{x \to 0} \frac{\sin x}{x}
   ```

### 工作流执行

1. **问题识别**: 系统分析输入内容，提取数学问题
2. **解答生成**: AI生成详细的解题步骤和概念解释
3. **质量审计**: 审计专家验证答案准确性
4. **结果展示**: 显示最终的经过审计的解答

### 结果查看

- **问题识别结果**: 显示系统识别出的问题文本
- **最终解答**: 经过审计的完整解答和解释
- **审计意见**: 如果有问题，显示审计员的修正建议
- **重试信息**: 显示系统为确保准确性进行的重试次数

## 🎨 界面特性

### 主要组件

1. **页面头部**: 系统介绍和功能说明
2. **侧边栏**: 配置面板和系统信息
3. **输入区域**: 多标签页输入界面
4. **示例区域**: 使用示例和格式说明
5. **结果展示**: 分层次的结果显示
6. **历史记录**: 最近10次解答记录

### 用户体验

- **进度指示**: 实时显示工作流执行进度
- **状态反馈**: 清晰的成功/错误/警告提示
- **响应式布局**: 自适应不同设备屏幕
- **快捷操作**: 一键清空历史、重新执行等

## ⚙️ 配置说明

### Streamlit配置

配置文件位于 `.streamlit/config.toml`：

```toml
[theme]
primaryColor = "#1f77b4"
backgroundColor = "#ffffff"
secondaryBackgroundColor = "#f0f2f6"
textColor = "#262730"

[server]
port = 8501
maxUploadSize = 200
```

### 环境变量

确保设置了必要的API密钥：

```bash
# 如果使用OpenAI
export OPENAI_API_KEY="your-openai-api-key"

# 如果使用Google Gemini
export GOOGLE_API_KEY="your-google-api-key"
```

## 🔧 开发说明

### 项目结构

```
streamlit_app.py              # 主应用文件
.streamlit/
  └── config.toml            # Streamlit配置
workflow/
  └── demo.py               # LangGraph工作流
README_STREAMLIT.md         # 本文档
```

### 核心类

- `StreamlitMathTutorInterface`: 主界面类
  - `setup_page_config()`: 页面配置
  - `render_sidebar()`: 侧边栏渲染
  - `render_input_section()`: 输入区域渲染
  - `execute_workflow_with_progress()`: 工作流执行
  - `render_results_section()`: 结果展示

### 扩展开发

1. **添加新的输入类型**: 修改 `render_input_section()` 方法
2. **自定义结果展示**: 扩展 `render_results_section()` 方法
3. **增加配置选项**: 在 `render_sidebar()` 中添加新的控件
4. **优化用户体验**: 调整CSS样式和布局

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   streamlit run streamlit_app.py --server.port 8502
   ```

2. **依赖缺失**
   ```bash
   pip install streamlit pillow
   ```

3. **图片上传失败**
   - 检查文件格式是否支持
   - 确认文件大小不超过200MB

4. **LaTeX渲染问题**
   - 检查LaTeX语法是否正确
   - 确保使用支持的LaTeX命令

### 日志调试

启用详细日志：

```bash
streamlit run streamlit_app.py --logger.level debug
```

## 📝 更新日志

### v1.0.0 (2025-08-05)
- ✨ 初始版本发布
- 🎯 支持多种输入方式
- 📊 实时进度显示
- 📚 历史记录功能
- 🎨 响应式界面设计

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

本项目采用MIT许可证。
