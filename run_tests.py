#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试运行脚本

用于执行SQLAlchemy向量存储管理器的测试用例
"""

import subprocess  # nosec
import sys
from pathlib import Path


def run_tests() -> None:
    """运行测试用例。"""
    print("🚀 开始运行SQLAlchemy向量存储管理器测试...")
    print("=" * 60)

    # 获取项目根目录
    project_root = Path(__file__).parent

    try:
        # 使用uv运行测试
        cmd = [
            "uv",
            "run",
            "pytest",
            "tests/test_sqlalchemy_store.py",
            "-v",
            "--tb=short",
            "--disable-warnings",
        ]

        print(f"执行命令: {' '.join(cmd)}")
        print("-" * 60)

        # 运行测试
        result = subprocess.run(cmd, cwd=project_root, capture_output=False)  # nosec

        print("-" * 60)
        if result.returncode == 0:
            print("✅ 所有测试通过！")
        else:
            print("❌ 测试失败！")
            sys.exit(1)

    except FileNotFoundError:
        print("❌ 错误：未找到uv命令，请确保已安装uv")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 运行测试时发生错误：{e}")
        sys.exit(1)


def run_specific_test(test_name: str) -> None:
    """运行特定的测试用例"""
    print(f"🚀 运行特定测试：{test_name}")
    print("=" * 60)

    project_root = Path(__file__).parent

    try:
        cmd = [
            "uv",
            "run",
            "pytest",
            f"tests/test_sqlalchemy_store.py::{test_name}",
            "-v",
            "--tb=short",
            "--disable-warnings",
        ]

        print(f"执行命令: {' '.join(cmd)}")
        print("-" * 60)

        result = subprocess.run(cmd, cwd=project_root, capture_output=False)  # nosec

        print("-" * 60)
        if result.returncode == 0:
            print(f"✅ 测试 {test_name} 通过！")
        else:
            print(f"❌ 测试 {test_name} 失败！")
            sys.exit(1)

    except Exception as e:
        print(f"❌ 运行测试时发生错误：{e}")
        sys.exit(1)


def show_available_tests() -> None:
    """显示可用的测试用例"""
    print("📋 可用的测试用例：")
    print("=" * 60)

    tests = [
        "TestSQLAlchemyVectorStoreManager::test_initialize",
        "TestSQLAlchemyVectorStoreManager::test_add_documents",
        "TestSQLAlchemyVectorStoreManager::test_add_texts",
        "TestSQLAlchemyVectorStoreManager::test_similarity_search",
        "TestSQLAlchemyVectorStoreManager::test_similarity_search_with_score",
        "TestSQLAlchemyVectorStoreManager::test_similarity_search_with_filter",
        "TestSQLAlchemyVectorStoreManager::test_get_document_by_id",
        "TestSQLAlchemyVectorStoreManager::test_update_document",
        "TestSQLAlchemyVectorStoreManager::test_delete_documents",
        "TestSQLAlchemyVectorStoreManager::test_get_document_count",
        "TestSQLAlchemyVectorStoreManager::test_clear_all",
        "TestSQLAlchemyVectorStoreManager::test_integration_workflow",
    ]

    for i, test in enumerate(tests, 1):
        print(f"{i:2d}. {test}")

    print("\n使用方法：")
    print("  python run_tests.py                    # 运行所有测试")
    print("  python run_tests.py --list             # 显示可用测试")
    print("  python run_tests.py --test <test_name> # 运行特定测试")


if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "--list":
            show_available_tests()
        elif sys.argv[1] == "--test" and len(sys.argv) > 2:
            run_specific_test(sys.argv[2])
        else:
            print("❌ 无效的参数")
            print("使用方法：")
            print("  python run_tests.py --list             # 显示可用测试")
            print("  python run_tests.py --test <test_name> # 运行特定测试")
            sys.exit(1)
    else:
        run_tests()
