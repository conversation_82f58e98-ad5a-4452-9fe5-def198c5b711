#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/1/27 10:00
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : gradio_interface.py
# @Update  : 2025/1/27 10:00 新增Gradio界面

"""
Gradio界面模块

提供基于Gradio的Web界面，用于展示和操作向量存储功能。
"""

import asyncio
import logging
import threading
from typing import Any, Coroutine, Optional, Dict, Generator

import gradio as gr

from config.logging_handler import configure_logging, logger
from vectorstore import EmbeddingManager
from vectorstore.sqlalchemy_store import VectorStore

# 导入数学教辅智能体相关模块
from workflow.demo import app as math_tutor_app


class VectorStoreInterface:
    """向量存储界面类"""

    def __init__(self) -> None:
        """初始化向量存储界面"""
        self.vector_store: Optional[VectorStore] = None
        self.embedding_manager: Optional[EmbeddingManager] = None
        self._loop: Optional[asyncio.AbstractEventLoop] = None
        self._loop_thread: Optional[threading.Thread] = None
        self._loop_lock = threading.Lock()
        self._initialize_components()

    def _initialize_components(self) -> None:
        """初始化向量存储组件"""
        try:
            self.embedding_manager = EmbeddingManager()
            self.vector_store = VectorStore(embedding_manager=self.embedding_manager)
            self._setup_event_loop()
            logger.info("向量存储组件初始化成功")
        except Exception as e:
            logger.error(f"向量存储组件初始化失败: {str(e)}")
            raise

    def _setup_event_loop(self) -> None:
        """设置专用事件循环"""

        def run_event_loop() -> None:
            """在新线程中运行事件循环"""
            self._loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self._loop)
            self._loop.run_forever()

        self._loop_thread = threading.Thread(target=run_event_loop, daemon=True)
        self._loop_thread.start()

        # 等待事件循环启动
        while self._loop is None:
            import time

            time.sleep(0.01)

    def _run_async(self, coro: Coroutine[Any, Any, str]) -> str:
        """在专用事件循环中运行协程
        :param coro: 要运行的协程
        :return: 协程的执行结果
        """
        if self._loop is None:
            raise RuntimeError("事件循环未初始化")

        future = asyncio.run_coroutine_threadsafe(coro, self._loop)
        return future.result()

    def add_texts(self, texts: str) -> str:
        """
        添加文本到向量存储

        Args:
            texts: 要添加的文本

        Returns:
            操作结果信息
        """
        try:
            if not texts.strip():
                return "错误：请输入要添加的文本"

            # 直接使用输入的文本，不按行分割
            text_list = [texts.strip()]

            if not text_list:
                return "错误：没有有效的文本内容"

            # 在专用事件循环中运行异步函数
            async def _add_texts() -> str:
                if self.vector_store is None:
                    return "错误：向量存储未初始化"
                await self.vector_store.add_texts(texts=text_list)
                count = await self.vector_store.get_document_count()
                return f"成功添加 1 个文档到向量存储\n当前文档总数: {count}"

            return self._run_async(_add_texts())

        except Exception as e:
            error_msg = f"添加文本失败: {str(e)}"
            logger.error(error_msg)
            return error_msg

    def get_document_count(self) -> str:
        """
        获取文档数量

        Returns:
            文档数量信息
        """
        try:

            async def _get_count() -> str:
                if self.vector_store is None:
                    return "错误：向量存储未初始化"
                count = await self.vector_store.get_document_count()
                return f"当前向量存储中的文档数量: {count}"

            # 在专用事件循环中运行异步函数
            return self._run_async(_get_count())

        except Exception as e:
            error_msg = f"获取文档数量失败: {str(e)}"
            logger.error(error_msg)
            return error_msg

    def search_similar(
        self, query: str, top_k: int = 5, similarity_threshold: float = 0.0
    ) -> str:
        """
        搜索相似文档

        Args:
            query: 查询文本
            top_k: 返回结果数量
            similarity_threshold: 相似度阈值（0.0-1.0）

        Returns:
            搜索结果
        """
        try:
            if not query.strip():
                return "错误：请输入查询文本"

            if top_k < 1 or top_k > 20:
                return "错误：top_k 参数应在 1-20 之间"

            if similarity_threshold < 0.0 or similarity_threshold > 1.0:
                return "错误：相似度阈值应在 0.0-1.0 之间"

            async def _search() -> str:
                if self.vector_store is None:
                    return "错误：向量存储未初始化"
                # 执行相似性搜索（带分数）
                results = await self.vector_store.similarity_search_with_score(
                    query, k=top_k
                )

                if not results:
                    return "未找到相关文档"

                # 过滤相似度低于阈值的结果
                filtered_results = [
                    (document, similarity)
                    for document, similarity in results
                    if similarity >= similarity_threshold
                ]

                if not filtered_results:
                    return f"未找到相似度 >= {similarity_threshold:.2f} 的文档"

                # 格式化搜索结果
                result_text = f"查询: {query}\n"
                result_text += f"相似度阈值: {similarity_threshold:.2f}\n"
                result_text += f"找到 {len(filtered_results)} 个相关文档:\n\n"

                for i, (document, similarity) in enumerate(filtered_results, 1):
                    result_text += f"{i}. 相似度: {similarity:.4f}\n"
                    result_text += f"   内容: {document.page_content[:200]}...\n\n"

                return result_text

            # 在专用事件循环中运行异步函数
            return self._run_async(_search())

        except Exception as e:
            error_msg = f"搜索失败: {str(e)}"
            logger.error(error_msg)
            return error_msg

    def clear_all_documents(self) -> str:
        """
        清空所有文档

        Returns:
            操作结果信息
        """
        try:

            async def _clear_all() -> str:
                if self.vector_store is None:
                    return "错误：向量存储未初始化"
                # 获取当前文档数量
                count = await self.vector_store.get_document_count()

                if count == 0:
                    return "向量存储中已经没有文档"

                # 清空所有文档
                await self.vector_store.clear_all()

                return f"成功清空 {count} 个文档"

            # 在专用事件循环中运行异步函数
            return self._run_async(_clear_all())

        except Exception as e:
            error_msg = f"清空文档失败: {str(e)}"
            logger.error(error_msg)
            return error_msg

    def __del__(self) -> None:
        """析构函数，清理资源"""
        if self._loop and not self._loop.is_closed():
            self._loop.call_soon_threadsafe(self._loop.stop)
            if self._loop_thread and self._loop_thread.is_alive():
                self._loop_thread.join(timeout=1.0)


class MathTutorInterface:
    """数学教辅智能体界面类"""

    def __init__(self) -> None:
        """初始化数学教辅智能体界面"""
        self.app = math_tutor_app
        logger.info("数学教辅智能体界面初始化成功")

    def solve_math_problem(
        self, 
        problem_text: str, 
        education_level: str, 
        age: int
    ) -> Generator[str, None, None]:
        """
        解决数学问题的流式输出函数

        Args:
            problem_text: 数学问题文本
            education_level: 教育水平（初中、高中等）
            age: 年龄

        Yields:
            流式输出的解答内容
        """
        try:
            if not problem_text.strip():
                yield "错误：请输入数学问题"
                return

            # 准备输入数据
            inputs = {
                "input_content": problem_text.strip(),
                "input_type": "text",
                "user_profile": {"education": education_level, "age": age},
                "retry_count": 0,
            }

            # 执行工作流并流式输出
            full_response = ""
            current_node = ""
            step_messages = []
            
            # 初始化输出
            yield "🤖 开始处理数学问题...\n\n"
            
            # 使用 updates 模式来获取状态更新
            for state in self.app.stream(inputs, stream_mode="updates"):
                # 检查当前节点
                current_node = list(state.keys())[0] if state else ""
                
                # 显示处理进度
                if current_node == "recognizer":
                    step_messages = ["🔍 **步骤1：问题识别**\n正在分析问题类型..."]
                elif current_node == "solver":
                    step_messages = ["🧮 **步骤2：生成解答**\n正在生成详细解答..."]
                elif current_node == "auditor":
                    step_messages = ["✅ **步骤3：质量审计**\n正在验证答案准确性..."]
                
                # 获取当前状态中的响应
                if "generated_response" in state.get(current_node, {}):
                    response = state[current_node]["generated_response"]
                    if response and response != full_response:
                        full_response = response
                        # 构建完整的输出内容
                        output_content = self._build_output_content(step_messages, full_response)
                        yield output_content
                
                # 检查最终响应
                if "final_response" in state.get(current_node, {}):
                    final_response = state[current_node]["final_response"]
                    if final_response and final_response != full_response:
                        full_response = final_response
                        # 构建完整的输出内容
                        output_content = self._build_output_content(step_messages, full_response)
                        yield output_content

            # 确保最终结果被输出
            if full_response:
                output_content = self._build_output_content(step_messages, full_response)
                yield output_content
            else:
                yield "❌ 处理完成，但未获得有效结果"

        except Exception as e:
            error_msg = f"❌ 数学问题解答失败: {str(e)}"
            logger.error(error_msg)
            yield error_msg

    def _build_output_content(self, step_messages: list, response: str) -> str:
        """
        构建完整的输出内容

        Args:
            step_messages: 步骤消息列表
            response: 响应内容

        Returns:
            完整的输出内容
        """
        content_parts = []
        
        # 添加步骤消息
        for step_msg in step_messages:
            content_parts.append(step_msg)
        
        # 添加响应内容
        if response:
            content_parts.append(f"\n## 📝 解答结果\n\n{response}")
        
        return "\n\n".join(content_parts)


def create_gradio_interface() -> Any:
    """
    创建Gradio界面

    Returns:
        Gradio界面实例
    """
    # 初始化向量存储界面
    vector_interface = VectorStoreInterface()
    
    # 初始化数学教辅智能体界面
    math_tutor_interface = MathTutorInterface()

    # 创建界面
    with gr.Blocks(title="智能系统管理平台", theme=gr.themes.Soft()) as demo:
        gr.Markdown("# 🚀 智能系统管理平台")
        gr.Markdown("这是一个集成了向量存储管理和数学教辅智能体的综合平台")

        with gr.Tab("📝 向量存储管理"):
            gr.Markdown("### 向量存储管理系统")
            
            with gr.Tab("📝 添加文档"):
                gr.Markdown("#### 添加文本到向量存储")
                add_text_input = gr.Textbox(
                    label="输入文本", placeholder="请输入要添加的文本...", lines=5
                )
                add_text_button = gr.Button("添加文档", variant="primary")
                add_text_output = gr.Textbox(label="操作结果", lines=3, interactive=False)

                add_text_button.click(
                    fn=vector_interface.add_texts,
                    inputs=add_text_input,
                    outputs=add_text_output,
                )

            with gr.Tab("🔍 搜索文档"):
                gr.Markdown("#### 相似性搜索")
                search_query = gr.Textbox(label="查询文本", placeholder="请输入要搜索的文本...", lines=2)
                search_top_k = gr.Slider(
                    minimum=1, maximum=20, value=5, step=1, label="返回结果数量 (top_k)"
                )
                search_threshold = gr.Slider(
                    minimum=0.0,
                    maximum=1.0,
                    value=0.0,
                    step=0.01,
                    label="相似度阈值 (similarity_threshold)",
                )
                search_button = gr.Button("搜索", variant="primary")
                search_output = gr.Textbox(label="搜索结果", lines=10, interactive=False)

                search_button.click(
                    fn=vector_interface.search_similar,
                    inputs=[search_query, search_top_k, search_threshold],
                    outputs=search_output,
                )

            with gr.Tab("📊 统计信息"):
                gr.Markdown("#### 向量存储统计")
                count_button = gr.Button("获取文档数量", variant="primary")
                count_output = gr.Textbox(label="统计结果", lines=2, interactive=False)

                count_button.click(
                    fn=vector_interface.get_document_count, inputs=[], outputs=count_output
                )

            with gr.Tab("🗑️ 管理操作"):
                gr.Markdown("#### 数据管理")
                clear_button = gr.Button("清空所有文档", variant="stop")
                clear_output = gr.Textbox(label="操作结果", lines=2, interactive=False)

                clear_button.click(
                    fn=vector_interface.clear_all_documents, inputs=[], outputs=clear_output
                )

        with gr.Tab("🧮 数学教辅智能体"):
            gr.Markdown("### AI数学教辅智能体")
            gr.Markdown("这是一个基于LangGraph的智能数学教辅系统，能够解答数学问题并提供详细解释")
            
            with gr.Row():
                with gr.Column(scale=1):
                    gr.Markdown("#### 输入设置")
                    problem_input = gr.Textbox(
                        label="数学问题",
                        placeholder="请输入数学问题，例如：求解方程 2x + 5 = 13",
                        lines=4
                    )
                    
                    education_level = gr.Dropdown(
                        choices=["小学", "初中", "高中", "大学"],
                        value="初中",
                        label="教育水平"
                    )
                    
                    age_input = gr.Slider(
                        minimum=6,
                        maximum=25,
                        value=14,
                        step=1,
                        label="年龄"
                    )
                    
                    solve_button = gr.Button("开始解答", variant="primary", size="lg")
                
                with gr.Column(scale=2):
                    gr.Markdown("#### 智能解答")
                    # 使用Markdown组件来支持数学公式渲染
                    solution_output = gr.Markdown(
                        label="解答结果",
                        value="等待输入数学问题...",
                        elem_classes=["math-output"]
                    )
            
            # 绑定事件
            solve_button.click(
                fn=math_tutor_interface.solve_math_problem,
                inputs=[problem_input, education_level, age_input],
                outputs=solution_output
            )
            
            # 添加示例
            gr.Markdown("#### 📋 使用示例")
            gr.Markdown(
                """
                **示例问题：**
                - 求解方程 2x + 5 = 13
                - 计算 ∫x²dx
                - 求函数 f(x) = x² + 3x + 2 的导数
                - 解不等式 3x - 7 > 8
                
                **系统特点：**
                - 🔍 **智能识别**：自动识别问题类型（方程、积分、导数等）
                - 📝 **详细解答**：提供完整的解题步骤和概念解释
                - ✅ **质量保证**：通过审计专家验证答案准确性
                - 🎯 **个性化**：根据教育水平和年龄调整解答难度
                """
            )

        # 添加示例
        gr.Markdown("### 📋 系统使用示例")
        gr.Markdown(
            """
        **向量存储示例:**
        ```
        添加文档示例:
        这是一个完整的文档内容，可以包含多行文本。
        系统会将整个文本作为一个文档添加到向量存储中。
        ```

        **数学教辅示例:**
        - 问题: "求解方程 3x - 7 = 8"
        - 教育水平: 初中
        - 年龄: 14岁
        - 系统会提供详细的解题步骤和概念解释
        """
        )

    return demo


def main() -> None:
    """主函数"""
    # 配置日志
    configure_logging("langgraph_demo", level=logging.INFO)
    logger.info("启动Gradio界面...")

    try:
        # 创建并启动Gradio界面
        demo = create_gradio_interface()
        demo.launch(server_name="127.0.0.1", server_port=7860, share=False, debug=True)
    except Exception as e:
        logger.error(f"启动Gradio界面失败: {str(e)}")
        logger.exception("详细错误信息:")
        return

    logger.info("Gradio界面启动成功")


if __name__ == "__main__":
    main()
