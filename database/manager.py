"""
数据库管理器

提供数据库连接管理和基本操作功能。
"""

from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional

from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.orm import sessionmaker

from config.config import config
from config.logging_handler import logger

from .models import Base


class DatabaseManager:
    """数据库管理器"""

    def __init__(self):
        """初始化数据库管理器"""
        self._async_engine: Optional[create_async_engine] = None
        self._sync_engine: Optional[create_engine] = None
        self._async_session_factory: Optional[async_sessionmaker] = None
        self._sync_session_factory: Optional[sessionmaker] = None

    @property
    def async_engine(self):
        """获取异步数据库引擎"""
        if self._async_engine is None:
            self._async_engine = create_async_engine(
                config.database.connection_string,
                echo=config.database.echo,
                pool_pre_ping=True,
                pool_recycle=3600,
            )
        return self._async_engine

    @property
    def sync_engine(self):
        """获取同步数据库引擎"""
        if self._sync_engine is None:
            self._sync_engine = create_engine(
                config.database.connection_string,
                echo=config.database.echo,
                pool_pre_ping=True,
                pool_recycle=3600,
            )
        return self._sync_engine

    @property
    def async_session_factory(self):
        """获取异步会话工厂"""
        if self._async_session_factory is None:
            self._async_session_factory = async_sessionmaker(
                self.async_engine,
                class_=AsyncSession,
                expire_on_commit=False,
            )
        return self._async_session_factory

    @property
    def sync_session_factory(self):
        """获取同步会话工厂"""
        if self._sync_session_factory is None:
            self._sync_session_factory = sessionmaker(
                self.sync_engine,
                expire_on_commit=False,
            )
        return self._sync_session_factory

    async def initialize(self) -> None:
        """初始化数据库连接"""
        try:
            # 测试连接
            async with self.async_engine.begin() as conn:
                await conn.execute(text("SELECT 1"))

            # 创建所有表（如果不存在）
            async with self.async_engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)

            # 向量索引通过 __table_args__ 自动创建，无需手动调用
            logger.info("✅ 数据库初始化完成，向量索引将通过表创建自动生成")

        except Exception as e:
            raise RuntimeError(f"数据库初始化失败: {str(e)}")

    @asynccontextmanager
    async def get_async_session(self) -> AsyncGenerator[AsyncSession, None]:
        """获取异步数据库会话"""
        async with self.async_session_factory() as session:
            try:
                yield session
                await session.commit()
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()

    def get_sync_session(self):
        """获取同步数据库会话"""
        return self.sync_session_factory()

    async def check_connection(self) -> bool:
        """检查数据库连接状态"""
        try:
            async with self.async_engine.begin() as conn:
                result = await conn.execute(text("SELECT 1"))
                return result.scalar() == 1
        except Exception:
            return False

    async def get_database_info(self) -> dict:
        """获取数据库信息"""
        try:
            async with self.async_engine.begin() as conn:
                # 获取PostgreSQL版本
                version_result = await conn.execute(text("SELECT version()"))
                version = version_result.scalar()

                # 检查pgvector扩展
                extension_result = await conn.execute(
                    text("SELECT extname FROM pg_extension WHERE extname = 'vector'")
                )
                pgvector_installed = extension_result.scalar() is not None

                # 获取数据库大小
                size_result = await conn.execute(
                    text("SELECT pg_size_pretty(pg_database_size(current_database()))")
                )
                db_size = size_result.scalar()

                return {
                    "version": version,
                    "pgvector_installed": pgvector_installed,
                    "database_size": db_size,
                    "connection_string": config.database.connection_string.replace(
                        config.database.password, "***"
                    ),
                }
        except Exception as e:
            return {"error": str(e)}

    async def close(self) -> None:
        """关闭数据库连接"""
        if self._async_engine:
            await self._async_engine.dispose()
        if self._sync_engine:
            self._sync_engine.dispose()


# 全局数据库管理器实例
db_manager = DatabaseManager()
