# 数据库模块

本模块提供了数据库模型定义和数据库管理功能。

## 模型设计

### 通用字段混入类

为了减少代码重复和提高可维护性，我们提供了两个通用的混入类：

#### `CommonFieldsMixin`

提供通用的ID字段：

- `id`: 主键ID，自动递增

#### `TimestampMixin`

提供通用的时间戳字段：

- `created_at`: 创建时间，自动设置为当前时间
- `updated_at`: 更新时间，在记录更新时自动更新

### 使用示例

```python
from database.models import CommonFieldsMixin, TimestampMixin, Base


class MyModel(CommonFieldsMixin, TimestampMixin, Base):
    """
    自定义模型示例

    继承通用字段混入类，自动获得：
    - id: 主键ID
    - created_at: 创建时间
    - updated_at: 更新时间
    """
    __tablename__ = "my_table"

    # 自定义字段
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    def __repr__(self) -> str:
        return f"<MyModel(id={self.id}, name='{self.name}')>"
```

### 现有模型

#### `VectorDocument`

向量文档表，用于存储文档的向量表示和元数据信息。

字段：

- `content`: 文档内容
- `embedding`: 向量表示
- `source`: 文档来源
- `document_type`: 文档类型
- `id`: 主键ID（来自CommonFieldsMixin）
- `created_at`: 创建时间（来自TimestampMixin）
- `updated_at`: 更新时间（来自TimestampMixin）

索引：

- 元数据查询索引：`source`, `document_type`, `created_at`
- 复合索引：`source + document_type`
- HNSW向量索引：用于高效的向量相似度搜索

#### `User`（示例模型）

用户表示例，展示如何使用混入类。

字段：

- `username`: 用户名（唯一）
- `email`: 邮箱地址（唯一）
- `is_active`: 是否激活
- 通用字段：`id`, `created_at`, `updated_at`

## 向量索引

### HNSW索引

我们使用HNSW（Hierarchical Navigable Small World）索引来优化向量相似度搜索：

```python
Index(
    'idx_embedding_hnsw',
    'embedding',
    postgresql_using='hnsw',
    postgresql_with={'m': 16, 'ef_construction': 64},
    postgresql_ops={'embedding': 'vector_cosine_ops'}
)
```

参数说明：

- `m`: 每个节点的最大连接数（16）
- `ef_construction`: 构建时的搜索深度（64）
- `vector_cosine_ops`: 使用余弦相似度操作符

### 索引创建

向量索引通过`__table_args__`自动创建，无需手动调用`create_vector_index`方法。

## 数据库迁移

### 创建迁移

```bash
uv run alembic revision --autogenerate -m "描述"
```

### 应用迁移

```bash
uv run alembic upgrade head
```

### 回滚迁移

```bash
uv run alembic downgrade -1
```

## 最佳实践

1. **继承顺序**：混入类应该在Base类之前
   ```python
   class MyModel(CommonFieldsMixin, TimestampMixin, Base):
   ```

2. **字段注释**：为所有字段添加清晰的注释
   ```python
   name: Mapped[str] = mapped_column(String(100), comment="名称")
   ```

3. **索引优化**：为常用查询字段创建索引
   ```python
   __table_args__ = (
       Index('idx_name', 'name'),
       Index('idx_status', 'status'),
   )
   ```

4. **类型提示**：使用完整的类型注解
   ```python
   from typing import Optional

   description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
   ```

5. **方法实现**：实现`__repr__`和`to_dict`方法
   ```python
   def __repr__(self) -> str:
       return f"<{self.__class__.__name__}(id={self.id})>"

   def to_dict(self) -> dict:
       return {
           'id': self.id,
           'name': self.name,
           'created_at': self.created_at.isoformat(),
       }
   ```
