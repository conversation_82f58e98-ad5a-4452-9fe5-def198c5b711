"""
数据库迁移管理器

使用Alembic管理数据库架构变更。
"""
# flake8: noqa E501
from pathlib import Path
from typing import List, Optional

from alembic import command
from alembic.config import Config
from alembic.script import ScriptDirectory
from config.config import config


class MigrationManager:
    """Alembic迁移管理器"""

    def __init__(self, alembic_cfg_path: Optional[str] = None):
        """
        初始化迁移管理器

        Args:
            alembic_cfg_path: Alembic配置文件路径
        """
        self.alembic_cfg_path = alembic_cfg_path or self._get_alembic_cfg_path()
        self.alembic_cfg = Config(self.alembic_cfg_path)

    def _get_alembic_cfg_path(self) -> str:
        """获取Alembic配置文件路径"""
        # 查找项目根目录下的alembic.ini文件
        project_root = Path(__file__).parent.parent
        alembic_cfg_path = project_root / "alembic.ini"

        if not alembic_cfg_path.exists():
            # 如果不存在，创建默认配置
            self._create_alembic_config(alembic_cfg_path)

        return str(alembic_cfg_path)

    def _create_alembic_config(self, config_path: Path) -> None:
        """创建Alembic配置文件"""
        config_content = f"""[alembic]
# path to migration scripts
script_location = alembic

# template used to generate migration file names; The default value is %%(rev)s_%%(slug)s
# Uncomment the line below if you want the files to be prepended with date and time
# file_template = %%(year)d_%%(month).2d_%%(day).2d_%%(hour).2d%%(minute).2d-%%(rev)s_%%(slug)s

# sys.path path, will be prepended to sys.path if present.
# defaults to the current working directory.
prepend_sys_path = .

# timezone to use when rendering the date within the migration file
# as well as the filename.
# If specified, requires the python-dateutil library that can be
# installed by adding `alembic[tz]` to the pip requirements
# string value is passed to dateutil.tz.gettz()
# leave blank for localtime
# timezone =

# max length of characters to apply to the
# "slug" field
# truncate_slug_length = 40

# set to 'true' to run the environment during
# the 'revision' command, regardless of autogenerate
# revision_environment = false

# set to 'true' to allow .pyc and .pyo files without
# a source .py file to be detected as revisions in the
# versions/ directory
# sourceless = false

# version number format
version_num_format = %04d

# version path separator; As mentioned above, this is the character used to split
# version_locations. The default within new alembic.ini files is "os", which uses
# os.pathsep. If this key is omitted entirely, it falls back to the legacy
# behavior of splitting on spaces and/or commas.
# Valid values for version_path_separator are:
#
# version_path_separator = :
# version_path_separator = ;
# version_path_separator = space
version_path_separator = os

# the output encoding used when revision files
# are written from script.py.mako
# output_encoding = utf-8

sqlalchemy.url = {config.database.connection_string}


[post_write_hooks]
# post_write_hooks defines scripts or Python functions that are run
# on newly generated revision scripts.  See the documentation for further
# detail and examples

# format using "black" - use the console_scripts runner, against the "black" entrypoint
# hooks = black
# black.type = console_scripts
# black.entrypoint = black
# black.options = -l 79 REVISION_SCRIPT_FILENAME

# lint with attempts to fix using "ruff" - use the exec runner, execute a binary
# hooks = ruff
# ruff.type = exec
# ruff.executable = %(here)s/.venv/bin/ruff
# ruff.options = --fix REVISION_SCRIPT_FILENAME

# Logging configuration
[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = WARN
handlers = console
qualname =

[logger_sqlalchemy]
level = WARN
handlers =
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handlers =
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %(levelname)-5.5s [%(name)s] %(message)s
datefmt = %H:%M:%S
"""

        # 创建alembic目录
        alembic_dir = config_path.parent / "alembic"
        alembic_dir.mkdir(exist_ok=True)

        # 创建versions目录
        versions_dir = alembic_dir / "versions"
        versions_dir.mkdir(exist_ok=True)

        # 创建env.py文件
        env_py_path = alembic_dir / "env.py"
        if not env_py_path.exists():
            self._create_env_py(env_py_path)

        # 创建script.py.mako模板
        script_mako_path = alembic_dir / "script.py.mako"
        if not script_mako_path.exists():
            self._create_script_mako(script_mako_path)

        # 写入配置文件
        config_path.write_text(config_content, encoding="utf-8")

    def _create_env_py(self, env_py_path: Path) -> None:
        """创建env.py文件"""
        env_py_content = '''"""Alembic环境配置

此文件由Alembic自动生成，用于配置数据库迁移环境。
"""

import asyncio
from logging.config import fileConfig

from sqlalchemy import pool
from sqlalchemy.engine import Connection
from sqlalchemy.ext.asyncio import async_engine_from_config

from alembic import context

# 导入项目配置和模型
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.config import config
from database.models import Base

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config_obj = context.config

# 设置数据库URL
config_obj.set_main_option("sqlalchemy.url", config.database.connection_string)

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config_obj.config_file_name is not None:
    fileConfig(config_obj.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
target_metadata = Base.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config_obj.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config_obj.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def do_run_migrations(connection: Connection) -> None:
    context.configure(connection=connection, target_metadata=target_metadata)

    with context.begin_transaction():
        context.run_migrations()


async def run_async_migrations() -> None:
    """In this scenario we need to create an Engine
    and associate a connection with the context.

    """

    connectable = async_engine_from_config(
        config_obj.get_section(config_obj.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    async with connectable.connect() as connection:
        await connection.run_sync(do_run_migrations)

    await connectable.dispose()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """

    asyncio.run(run_async_migrations())


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
'''

        env_py_path.write_text(env_py_content, encoding="utf-8")

    def _create_script_mako(self, script_mako_path: Path) -> None:
        """创建script.py.mako模板文件"""
        script_mako_content = '''"""${message}

Revision ID: ${up_revision}
Revises: ${down_revision | comma,n}
Create Date: ${create_date}

"""
from alembic import op
import sqlalchemy as sa
${imports if imports else ""}

# revision identifiers, used by Alembic.
revision = ${repr(up_revision)}
down_revision = ${repr(down_revision)}
branch_labels = ${repr(branch_labels)}
depends_on = ${repr(depends_on)}


def upgrade() -> None:
    ${upgrades if upgrades else "pass"}


def downgrade() -> None:
    ${downgrades if downgrades else "pass"}
'''

        script_mako_path.write_text(script_mako_content, encoding="utf-8")

    def init(self) -> None:
        """初始化Alembic环境"""
        try:
            command.init(self.alembic_cfg, "alembic")
            print("Alembic环境初始化成功")
        except Exception as e:
            print(f"Alembic环境初始化失败: {e}")

    def create_migration(self, message: str) -> Optional[str]:
        """
        创建新的迁移文件

        Args:
            message: 迁移描述信息

        Returns:
            迁移文件路径
        """
        try:
            command.revision(self.alembic_cfg, message=message, autogenerate=True)

            # 获取最新的迁移文件
            script_dir = ScriptDirectory.from_config(self.alembic_cfg)
            head_revision = script_dir.get_current_head()

            if head_revision:
                migration_file = script_dir.get_revision(head_revision)
                return migration_file.path

            return None
        except Exception as e:
            print(f"创建迁移文件失败: {e}")
            return None

    def upgrade(self, revision: str = "head") -> bool:
        """
        执行数据库升级

        Args:
            revision: 目标版本，默认为最新版本

        Returns:
            是否成功
        """
        try:
            command.upgrade(self.alembic_cfg, revision)
            print(f"数据库升级到版本 {revision} 成功")
            return True
        except Exception as e:
            print(f"数据库升级失败: {e}")
            return False

    def downgrade(self, revision: str) -> bool:
        """
        执行数据库降级

        Args:
            revision: 目标版本

        Returns:
            是否成功
        """
        try:
            command.downgrade(self.alembic_cfg, revision)
            print(f"数据库降级到版本 {revision} 成功")
            return True
        except Exception as e:
            print(f"数据库降级失败: {e}")
            return False

    def current(self) -> Optional[str]:
        """
        获取当前数据库版本

        Returns:
            当前版本号
        """
        try:
            command.current(self.alembic_cfg)
            return "current"
        except Exception as e:
            print(f"获取当前版本失败: {e}")
            return None

    def history(self) -> List[str]:
        """
        获取迁移历史

        Returns:
            迁移历史列表
        """
        try:
            command.history(self.alembic_cfg)
            return []
        except Exception as e:
            print(f"获取迁移历史失败: {e}")
            return []

    def stamp(self, revision: str = "head") -> bool:
        """
        标记数据库版本（不执行迁移）

        Args:
            revision: 要标记的版本

        Returns:
            是否成功
        """
        try:
            command.stamp(self.alembic_cfg, revision)
            print(f"数据库标记为版本 {revision} 成功")
            return True
        except Exception as e:
            print(f"数据库标记失败: {e}")
            return False


# 全局迁移管理器实例
migration_manager = MigrationManager()
