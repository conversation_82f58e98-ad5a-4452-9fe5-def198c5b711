"""
数据库模型定义

使用SQLAlchemy ORM定义数据库表结构。
"""

from datetime import datetime

from pgvector.sqlalchemy import Vector
from sqlalchemy import DateTime, Index, Integer, String, Text
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column

from config.config import config


class Base(DeclarativeBase):
    """SQLAlchemy声明式基类"""

    pass


class TimestampMixin:
    """
    时间戳混入类

    提供创建时间和更新时间的通用字段。
    """

    # 创建时间
    created_at: Mapped[datetime] = mapped_column(
        DateTime, nullable=False, default=datetime.utcnow, comment="创建时间"
    )

    # 更新时间
    updated_at: Mapped[datetime] = mapped_column(
        DateTime,
        nullable=False,
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        comment="更新时间",
    )


class CommonFieldsMixin:
    """
    通用字段混入类

    提供ID字段的通用定义。
    """

    # 主键ID
    id: Mapped[int] = mapped_column(
        Integer, primary_key=True, autoincrement=True, comment="主键ID"
    )


class VectorDocument(CommonFieldsMixin, TimestampMixin, Base):
    """
    向量文档表模型

    存储文档的向量表示和元数据信息。
    """

    __tablename__ = "vector_documents"

    # 文档内容
    content: Mapped[str] = mapped_column(Text, nullable=False, comment="文档内容")

    # 向量表示 - 从配置中获取维度
    embedding: Mapped[list] = mapped_column(
        Vector(config.vector_store.vector_size),  # 从配置获取向量维度
        nullable=False,
        comment="文档的向量表示",
    )

    # 元数据字段
    source: Mapped[str] = mapped_column(
        String(255), nullable=False, default="unknown", comment="文档来源"
    )

    document_type: Mapped[str] = mapped_column(
        String(100), nullable=False, default="text", comment="文档类型"
    )

    # 索引定义
    __table_args__ = (
        # 元数据查询索引
        Index("idx_source", "source"),
        Index("idx_document_type", "document_type"),
        Index("idx_created_at", "created_at"),
        # 复合索引
        Index("idx_source_type", "source", "document_type"),
        # HNSW向量索引 - 用于高效的向量相似度搜索
        Index(
            "idx_embedding_hnsw",
            "embedding",
            postgresql_using="hnsw",
            postgresql_with={"m": 16, "ef_construction": 64},
            postgresql_ops={"embedding": "vector_cosine_ops"},
        ),
    )

    def __repr__(self) -> str:
        """字符串表示"""
        return f"<VectorDocument(id={self.id}, source='{self.source}', type='{self.document_type}')>"  # noqa E501

    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            "id": self.id,
            "content": self.content,
            "embedding": self.embedding,
            "source": self.source,
            "document_type": self.document_type,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


# 示例：如何使用混入类创建新的表
class User(CommonFieldsMixin, TimestampMixin, Base):
    """
    用户表示例

    展示如何使用混入类创建包含通用字段的新表。
    """

    __tablename__ = "users"

    # 用户特定字段
    username: Mapped[str] = mapped_column(
        String(50), nullable=False, unique=True, comment="用户名"
    )

    email: Mapped[str] = mapped_column(
        String(100), nullable=False, unique=True, comment="邮箱地址"
    )

    # 文化程度
    education: Mapped[str] = mapped_column(
        String(50), nullable=False, default="unknown", comment="文化程度"
    )

    # 年龄
    age: Mapped[int] = mapped_column(Integer, nullable=False, default=0, comment="年龄")

    # 性别
    gender: Mapped[str] = mapped_column(
        String(10), nullable=False, default="unknown", comment="性别"
    )

    is_active: Mapped[bool] = mapped_column(default=True, comment="是否激活")

    # 索引定义
    __table_args__ = (
        Index("idx_username", "username"),
        Index("idx_email", "email"),
        Index("idx_is_active", "is_active"),
    )

    def __repr__(self) -> str:
        """字符串表示"""
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"

    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            "id": self.id,
            "username": self.username,
            "email": self.email,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
