# Gradio界面使用说明

## 概述

本项目提供了一个基于Gradio的Web界面，用于管理和操作向量存储系统。界面采用现代化的设计，支持文档的添加、搜索、统计和管理等核心功能。

## 功能特性

### 📝 添加文档
- 支持批量添加文本文档
- 多个文档用换行符分隔
- 实时显示添加结果和文档总数

### 🔍 搜索文档
- 基于语义相似性的文档搜索
- 可调节返回结果数量（1-20个）
- 显示相似度分数和文档内容预览

### 📊 统计信息
- 实时查看向量存储中的文档数量
- 一键获取当前存储状态

### 🗑️ 管理操作
- 清空所有文档
- 安全确认机制

## 安装和启动

### 1. 安装依赖

确保已安装项目依赖：

```bash
uv sync
```

### 2. 启动Gradio界面

#### 方式一：直接启动
```bash
uv python gradio_app.py
```

#### 方式二：使用启动脚本
```bash
uv python start_gradio.py
```

### 3. 访问界面

启动成功后，在浏览器中访问：
```
http://localhost:7860
```

## 使用指南

### 添加文档

1. 点击"📝 添加文档"标签页
2. 在文本框中输入要添加的文档内容
3. 多个文档请用换行符分隔
4. 点击"添加文档"按钮
5. 查看操作结果

**示例输入：**
```
这是第一个文档的内容
这是第二个文档的内容
这是第三个文档的内容
```

### 搜索文档

1. 点击"🔍 搜索文档"标签页
2. 在"查询文本"框中输入搜索关键词
3. 调整"返回结果数量"滑块（1-20）
4. 点击"搜索"按钮
5. 查看搜索结果

**搜索示例：**
- 输入："文档"
- 系统会返回包含"文档"关键词的相关内容

### 查看统计信息

1. 点击"📊 统计信息"标签页
2. 点击"获取文档数量"按钮
3. 查看当前向量存储中的文档总数

### 管理操作

1. 点击"🗑️ 管理操作"标签页
2. 点击"清空所有文档"按钮
3. 确认操作（此操作不可撤销）

## 技术架构

### 核心组件

- **VectorStoreInterface**: 向量存储界面类，封装了所有业务逻辑
- **Gradio Blocks**: 使用Gradio Blocks API构建现代化界面
- **异步处理**: 正确处理异步操作，避免事件循环冲突

### 界面设计

- **响应式布局**: 适配不同屏幕尺寸
- **标签页设计**: 功能模块化，操作清晰
- **现代化主题**: 使用Gradio Soft主题
- **用户友好**: 提供详细的操作提示和示例

### 错误处理

- **输入验证**: 验证用户输入的有效性
- **异常捕获**: 捕获并显示友好的错误信息
- **日志记录**: 详细的操作日志记录

## 配置选项

### 服务器配置

在`gradio_app.py`中可以修改以下配置：

```python
demo.launch(
    server_name="0.0.0.0",  # 服务器地址
    server_port=7860,       # 端口号
    share=False,            # 是否分享到公网
    debug=True              # 调试模式
)
```

### 向量存储配置

向量存储的配置在`config.yaml`文件中：

```yaml
database:
  url: "postgresql+asyncpg://username:password@localhost/dbname"
  echo: false

vectorstore:
  embedding_model: "text-embedding-ada-002"
  collection_name: "documents"
```

## 故障排除

### 常见问题

1. **端口被占用**
   - 错误：`Address already in use`
   - 解决：修改`server_port`或停止占用端口的进程

2. **数据库连接失败**
   - 错误：`Connection refused`
   - 解决：检查数据库配置和连接状态

3. **异步操作错误**
   - 错误：`Event loop is closed`
   - 解决：已修复，使用新的事件循环处理异步操作

### 日志查看

查看详细日志：
```bash
tail -f logs/langgraph_demo.log
```

## 开发说明

### 代码结构

```
gradio_app.py          # 主界面文件
start_gradio.py        # 启动脚本
README_GRADIO.md       # 使用说明
```

### 扩展功能

如需添加新功能，可以：

1. 在`VectorStoreInterface`类中添加新方法
2. 在`create_gradio_interface()`函数中添加新的标签页
3. 配置相应的事件处理

### 测试

运行测试：
```bash
uv run pytest tests/
```

## 版本信息

- **当前版本**: 1.2.2
- **Gradio版本**: 5.39.0
- **Python版本**: >=3.12

## 更新日志

### [1.2.2] - 2025-01-27
- 新增：Gradio Web界面
- 新增：向量存储管理功能
- 新增：文档搜索功能
- 修复：异步事件循环冲突问题
- 优化：用户界面和交互体验
