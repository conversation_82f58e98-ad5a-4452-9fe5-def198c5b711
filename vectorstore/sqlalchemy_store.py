"""
基于SQLAlchemy的向量存储管理器

使用SQLAlchemy ORM和自定义向量存储功能，替代原有的pgvector_store.py。
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

from langchain_core.documents import Document
from sqlalchemy import func, select

from config.config import config
from database.manager import db_manager
from database.models import VectorDocument

from .embeddings import EmbeddingManager


class SQLAlchemyVectorStoreManager:
    """基于SQLAlchemy的向量存储管理器"""

    def __init__(self, embedding_manager: Optional[EmbeddingManager] = None):
        """
        初始化向量存储管理器

        Args:
            embedding_manager: 嵌入模型管理器
        """
        self.embedding_manager = embedding_manager or EmbeddingManager()
        # 从配置中获取向量维度
        self.vector_size = config.vector_store.vector_size

    async def initialize(self) -> None:
        """初始化向量存储"""
        try:
            # 初始化数据库连接
            await db_manager.initialize()

            # 验证pgvector扩展
            await self._verify_pgvector_extension()

            # 创建向量索引
            await self._create_vector_index()

        except Exception as e:
            raise RuntimeError(f"初始化向量存储失败: {str(e)}")

    @staticmethod
    async def _verify_pgvector_extension() -> None:
        """验证pgvector扩展是否已安装"""
        try:
            async with db_manager.get_async_session() as session:
                from sqlalchemy import text

                # 检查pgvector扩展是否存在
                result = await session.execute(
                    text("SELECT extname FROM pg_extension WHERE extname = 'vector'")
                )

                if result.fetchone() is None:
                    # 如果pgvector未安装，尝试创建扩展
                    try:
                        await session.execute(
                            text("CREATE EXTENSION IF NOT EXISTS vector")
                        )
                        await session.commit()
                        print("✅ pgvector扩展安装成功")
                    except Exception as ext_error:
                        raise RuntimeError(f"pgvector扩展安装失败: {str(ext_error)}")
                else:
                    print("✅ pgvector扩展已存在")

        except Exception as e:
            raise RuntimeError(f"pgvector扩展验证失败: {str(e)}")

    @staticmethod
    async def _create_vector_index() -> None:
        """创建向量索引"""
        try:
            async with db_manager.get_async_session() as session:
                # 创建HNSW索引用于高效的向量相似度搜索
                from sqlalchemy import text

                # 检查索引是否已存在
                result = await session.execute(
                    text(
                        """
                        SELECT indexname
                        FROM pg_indexes
                        WHERE tablename = 'vector_documents'
                          AND indexname = 'idx_embedding_hnsw'
                        """
                    )
                )

                if result.fetchone() is None:
                    # 创建HNSW索引
                    await session.execute(
                        text(
                            """
                            CREATE INDEX idx_embedding_hnsw
                                ON vector_documents
                                USING hnsw (embedding vector_cosine_ops)
                                WITH (m = 16, ef_construction = 64)
                            """
                        )
                    )
                    await session.commit()
                    print("✅ HNSW向量索引创建成功")
                else:
                    print("✅ HNSW向量索引已存在")

        except Exception as e:
            print(f"⚠️  向量索引创建失败（可能已存在）: {e}")
            # 不抛出异常，因为索引可能已经存在

    async def add_documents(self, documents: List[Document]) -> List[str]:
        """
        添加文档到向量存储

        Args:
            documents: 文档列表

        Returns:
            文档ID列表
        """
        try:
            ids = []
            async with db_manager.get_async_session() as session:
                for doc in documents:
                    # 生成文档嵌入
                    embedding = await self.embedding_manager.embed_query(
                        doc.page_content
                    )

                    # 准备元数据
                    metadata = doc.metadata or {}
                    current_time = datetime.utcnow()

                    # 使用ORM创建向量文档
                    vector_doc = VectorDocument(
                        content=doc.page_content,
                        embedding=embedding,
                        source=metadata.get("source", "unknown"),
                        document_type=metadata.get("document_type", "text"),
                        created_at=metadata.get("created_at", current_time),
                        updated_at=metadata.get("updated_at", current_time),
                    )

                    session.add(vector_doc)
                    await session.flush()  # 获取ID
                    ids.append(str(vector_doc.id))

                await session.commit()

            return ids

        except Exception as e:
            raise RuntimeError(f"添加文档失败: {str(e)}")

    async def add_texts(
        self, texts: List[str], metadatas: Optional[List[Dict[str, Any]]] = None
    ) -> List[str]:
        """
        添加文本到向量存储

        Args:
            texts: 文本列表
            metadatas: 元数据列表

        Returns:
            文档ID列表
        """
        try:
            if metadatas is None:
                metadatas = [{} for _ in texts]

            ids = []
            async with db_manager.get_async_session() as session:
                for i, text in enumerate(texts):
                    # 生成文本嵌入
                    embedding = await self.embedding_manager.embed_query(text)

                    # 准备元数据
                    metadata = metadatas[i] or {}
                    current_time = datetime.now()

                    # 使用ORM创建向量文档
                    vector_doc = VectorDocument(
                        content=text,
                        embedding=embedding,
                        source=metadata.get("source", "unknown"),
                        document_type=metadata.get("document_type", "text"),
                        created_at=metadata.get("created_at", current_time),
                        updated_at=metadata.get("updated_at", current_time),
                    )

                    session.add(vector_doc)
                    await session.flush()  # 获取ID
                    ids.append(str(vector_doc.id))

                await session.commit()

            return ids

        except Exception as e:
            raise RuntimeError(f"添加文本失败: {str(e)}")

    async def similarity_search(
        self, query: str, k: int = 5, filter_dict: Optional[Dict[str, Any]] = None
    ) -> List[Document]:
        """
        相似度搜索

        Args:
            query: 查询文本
            k: 返回结果数量
            filter_dict: 过滤条件

        Returns:
            相似文档列表
        """
        try:
            # 生成查询嵌入
            query_embedding = await self.embedding_manager.embed_query(query)

            async with db_manager.get_async_session() as session:
                # 构建基础查询
                stmt = (
                    select(VectorDocument)
                    .order_by(VectorDocument.embedding.cosine_distance(query_embedding))
                    .limit(k)
                )

                # 应用过滤条件
                if filter_dict:
                    if "source" in filter_dict:
                        stmt = stmt.where(
                            VectorDocument.source == filter_dict["source"]
                        )
                    if "document_type" in filter_dict:
                        stmt = stmt.where(
                            VectorDocument.document_type == filter_dict["document_type"]
                        )

                # 执行查询
                result = await session.execute(stmt)
                vector_docs = result.scalars().all()

                # 转换为Document对象
                documents = []
                for vector_doc in vector_docs:
                    document = Document(
                        page_content=vector_doc.content,
                        metadata={
                            "id": vector_doc.id,
                            "source": vector_doc.source,
                            "document_type": vector_doc.document_type,
                            "created_at": vector_doc.created_at.isoformat()
                            if vector_doc.created_at
                            else None,
                            "updated_at": vector_doc.updated_at.isoformat()
                            if vector_doc.updated_at
                            else None,
                        },
                    )
                    documents.append(document)

                return documents

        except Exception as e:
            raise RuntimeError(f"相似度搜索失败: {str(e)}")

    async def similarity_search_with_score(
        self, query: str, k: int = 5, filter_dict: Optional[Dict[str, Any]] = None
    ) -> List[Tuple[Document, float]]:
        """
        带分数的相似度搜索

        Args:
            query: 查询文本
            k: 返回结果数量
            filter_dict: 过滤条件

        Returns:
            文档和分数元组列表
        """
        try:
            # 生成查询嵌入
            query_embedding = await self.embedding_manager.embed_query(query)

            async with db_manager.get_async_session() as session:
                # 构建查询，计算相似度分数
                # 使用1减去余弦距离作为相似度分数
                stmt = (
                    select(
                        VectorDocument,
                        (
                            1
                            - VectorDocument.embedding.cosine_distance(query_embedding)
                        ).label("similarity"),
                    )
                    .order_by(VectorDocument.embedding.cosine_distance(query_embedding))
                    .limit(k)
                )

                # 应用过滤条件
                if filter_dict:
                    if "source" in filter_dict:
                        stmt = stmt.where(
                            VectorDocument.source == filter_dict["source"]
                        )
                    if "document_type" in filter_dict:
                        stmt = stmt.where(
                            VectorDocument.document_type == filter_dict["document_type"]
                        )

                # 执行查询
                result = await session.execute(stmt)
                rows = result.all()

                # 转换为Document和分数元组
                documents_with_scores = []
                for row in rows:
                    vector_doc = row[0]  # VectorDocument对象
                    similarity = row[1]  # 相似度分数

                    document = Document(
                        page_content=vector_doc.content,
                        metadata={
                            "id": vector_doc.id,
                            "source": vector_doc.source,
                            "document_type": vector_doc.document_type,
                            "created_at": vector_doc.created_at.isoformat()
                            if vector_doc.created_at
                            else None,
                            "updated_at": vector_doc.updated_at.isoformat()
                            if vector_doc.updated_at
                            else None,
                        },
                    )
                    documents_with_scores.append((document, float(similarity)))

                return documents_with_scores

        except Exception as e:
            raise RuntimeError(f"带分数的相似度搜索失败: {str(e)}")

    @staticmethod
    async def delete_documents(ids: List[str]) -> None:
        """
        删除文档

        Args:
            ids: 要删除的文档ID列表
        """
        try:
            async with db_manager.get_async_session() as session:
                # 转换ID为整数
                doc_ids = [int(doc_id) for doc_id in ids]

                # 使用ORM删除文档
                stmt = select(VectorDocument).where(VectorDocument.id.in_(doc_ids))
                result = await session.execute(stmt)
                vector_docs = result.scalars().all()

                # 删除找到的文档
                for vector_doc in vector_docs:
                    await session.delete(vector_doc)

                await session.commit()

        except Exception as e:
            raise RuntimeError(f"删除文档失败: {str(e)}")

    @staticmethod
    async def get_document_count() -> int:
        """
        获取文档数量

        Returns:
            文档数量
        """
        try:
            async with db_manager.get_async_session() as session:
                stmt = select(func.count(VectorDocument.id))
                result = await session.execute(stmt)
                return result.scalar() or 0

        except Exception as e:
            raise RuntimeError(f"获取文档数量失败: {str(e)}")

    @staticmethod
    async def clear_all() -> None:
        """清空所有文档"""
        try:
            async with db_manager.get_async_session() as session:
                # 使用ORM删除所有文档
                stmt = select(VectorDocument)
                result = await session.execute(stmt)
                vector_docs = result.scalars().all()

                for vector_doc in vector_docs:
                    await session.delete(vector_doc)

                await session.commit()

        except Exception as e:
            raise RuntimeError(f"清空文档失败: {str(e)}")

    @staticmethod
    async def get_document_by_id(doc_id: str) -> Optional[Document]:
        """
        根据ID获取文档

        Args:
            doc_id: 文档ID

        Returns:
            文档对象，如果不存在则返回None
        """
        try:
            async with db_manager.get_async_session() as session:
                stmt = select(VectorDocument).where(VectorDocument.id == int(doc_id))
                result = await session.execute(stmt)
                vector_doc = result.scalar_one_or_none()

                if vector_doc is None:
                    return None

                return Document(
                    page_content=vector_doc.content,
                    metadata={
                        "id": vector_doc.id,
                        "source": vector_doc.source,
                        "document_type": vector_doc.document_type,
                        "created_at": vector_doc.created_at.isoformat()
                        if vector_doc.created_at
                        else None,
                        "updated_at": vector_doc.updated_at.isoformat()
                        if vector_doc.updated_at
                        else None,
                    },
                )

        except Exception as e:
            raise RuntimeError(f"获取文档失败: {str(e)}")

    async def update_document(
        self, doc_id: str, content: str, metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        更新文档

        Args:
            doc_id: 文档ID
            content: 新的文档内容
            metadata: 新的元数据

        Returns:
            是否更新成功
        """
        try:
            async with db_manager.get_async_session() as session:
                # 查找文档
                stmt = select(VectorDocument).where(VectorDocument.id == int(doc_id))
                result = await session.execute(stmt)
                vector_doc = result.scalar_one_or_none()

                if vector_doc is None:
                    return False

                # 重新生成嵌入
                new_embedding = await self.embedding_manager.embed_query(content)

                # 更新文档内容
                vector_doc.content = content
                vector_doc.embedding = new_embedding
                vector_doc.updated_at = datetime.utcnow()

                # 更新元数据
                if metadata:
                    if "source" in metadata:
                        vector_doc.source = metadata["source"]
                    if "document_type" in metadata:
                        vector_doc.document_type = metadata["document_type"]

                await session.commit()
                return True

        except Exception as e:
            raise RuntimeError(f"更新文档失败: {str(e)}")

    @staticmethod
    async def close() -> None:
        """关闭连接"""
        await db_manager.close()


# 为了兼容性，提供别名
VectorStore = SQLAlchemyVectorStoreManager
