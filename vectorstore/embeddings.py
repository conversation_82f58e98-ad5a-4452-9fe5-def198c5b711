"""
嵌入模型管理模块

管理不同的嵌入模型，支持Ollama和OpenAI等提供商。
"""

from typing import List

from langchain_core.embeddings import Embeddings
from langchain_ollama import OllamaEmbeddings
from langchain_openai import OpenAIEmbeddings

from config.config import config


class EmbeddingManager:
    """嵌入模型管理器"""

    def __init__(self, provider: str = "ollama"):
        """
        初始化嵌入模型管理器

        Args:
            provider: 嵌入模型提供商 ("ollama" 或 "openai")
        """
        self.provider = provider
        self.embeddings = self._create_embeddings()

    def _create_embeddings(self) -> Embeddings:
        """
        创建嵌入模型实例

        Returns:
            嵌入模型实例
        """
        if self.provider == "ollama":
            return OllamaEmbeddings(
                model=config.model.ollama_embedding_model,
                base_url=config.model.ollama_base_url,
            )
        elif self.provider == "openai":
            if not config.model.openai_api_key:
                raise ValueError("OpenAI API密钥未配置")
            return OpenAIEmbeddings(
                model=config.model.openai_embedding_model,
                api_key=config.model.openai_api_key,
            )
        else:
            raise ValueError(f"不支持的嵌入模型提供商: {self.provider}")

    async def embed_texts(self, texts: List[str]) -> List[List[float]]:
        """
        为文本列表生成嵌入向量

        Args:
            texts: 文本列表

        Returns:
            嵌入向量列表
        """
        try:
            return await self.embeddings.aembed_documents(texts)
        except Exception:  # noqa: F841
            # 如果异步方法失败，尝试同步方法
            return self.embeddings.embed_documents(texts)

    async def embed_query(self, query: str) -> List[float]:
        """
        为查询生成嵌入向量

        Args:
            query: 查询文本

        Returns:
            嵌入向量
        """
        try:
            return await self.embeddings.aembed_query(query)
        except Exception:  # noqa: F841
            # 如果异步方法失败，尝试同步方法
            return self.embeddings.embed_query(query)

    def get_embedding_dimension(self) -> int:
        """
        获取嵌入向量维度

        Returns:
            向量维度
        """
        # 对于Ollama，通常使用配置中的向量大小
        if self.provider == "ollama":
            return config.vector_store.vector_size
        elif self.provider == "openai":
            # OpenAI text-embedding-3-small 的维度是1536
            if "small" in config.model.openai_embedding_model:
                return 1536
            else:
                return 3072
        else:
            return config.vector_store.vector_size

    def switch_provider(self, provider: str) -> None:
        """
        切换嵌入模型提供商

        Args:
            provider: 新的提供商
        """
        self.provider = provider
        self.embeddings = self._create_embeddings()

    def get_provider_info(self) -> dict:
        """
        获取当前提供商信息

        Returns:
            提供商信息字典
        """
        return {
            "provider": self.provider,
            "model": (
                config.model.ollama_embedding_model
                if self.provider == "ollama"
                else config.model.openai_embedding_model
            ),
            "dimension": self.get_embedding_dimension(),
        }
