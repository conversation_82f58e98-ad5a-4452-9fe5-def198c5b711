#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess  # nosec
import sys
from typing import List

"""
pre-commit 设置脚本

自动安装和配置 pre-commit 钩子
"""


def run_command(command: List[str], description: str) -> bool:
    """运行命令并返回是否成功"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(  # nosec
            command, check=True, capture_output=True, text=True, encoding="utf-8"
        )
        print(f"✅ {description} 成功")
        if result.stdout.strip():
            print(f"   输出: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败")
        print(f"   错误: {e.stderr.strip()}")
        return False


def check_uv_installed() -> bool:
    """检查 uv 是否已安装"""
    try:
        subprocess.run(["uv", "--version"], check=True, capture_output=True)  # nosec
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False


def install_dev_dependencies() -> bool:
    """安装开发依赖"""
    if not check_uv_installed():
        print("❌ uv 未安装，请先安装 uv")
        return False

    return run_command(
        ["uv", "add", "--group", "dev", "pre-commit"], "安装 pre-commit 开发依赖"
    )


def install_precommit_hooks() -> bool:
    """安装 pre-commit 钩子"""
    return run_command(["uv", "run", "pre-commit", "install"], "安装 pre-commit 钩子")


def install_precommit_hooks_all_stages() -> bool:
    """安装所有阶段的 pre-commit 钩子"""
    return run_command(
        [
            "uv",
            "run",
            "pre-commit",
            "install",
            "--hook-type",
            "pre-commit",
            "--hook-type",
            "pre-push",
        ],
        "安装所有阶段的 pre-commit 钩子",
    )


def update_precommit_hooks() -> bool:
    """更新 pre-commit 钩子"""
    return run_command(
        ["uv", "run", "pre-commit", "autoupdate"], "更新 pre-commit 钩子到最新版本"
    )


def run_precommit_all_files() -> bool:
    """在所有文件上运行 pre-commit"""
    return run_command(
        ["uv", "run", "pre-commit", "run", "--all-files"], "在所有文件上运行 pre-commit 检查"
    )


def show_help() -> None:
    """显示帮助信息"""
    print(
        """
🚀 pre-commit 设置脚本

用法:
    python scripts/setup_precommit.py [选项]

选项:
    install      安装 pre-commit 钩子
    update       更新 pre-commit 钩子到最新版本
    run          在所有文件上运行 pre-commit 检查
    all          执行完整的设置流程（安装依赖、钩子、运行检查）

示例:
    python scripts/setup_precommit.py install
    python scripts/setup_precommit.py update
    python scripts/setup_precommit.py run
    python scripts/setup_precommit.py all
"""
    )


def main() -> int:
    """主函数"""
    if len(sys.argv) < 2:
        show_help()
        return 1

    command = sys.argv[1].lower()

    if command == "install":
        success = True
        success &= install_dev_dependencies()
        success &= install_precommit_hooks_all_stages()
        return 0 if success else 1

    elif command == "update":
        success = update_precommit_hooks()
        return 0 if success else 1

    elif command == "run":
        success = run_precommit_all_files()
        return 0 if success else 1

    elif command == "all":
        success = True
        success &= install_dev_dependencies()
        success &= install_precommit_hooks_all_stages()
        success &= update_precommit_hooks()
        success &= run_precommit_all_files()
        return 0 if success else 1

    else:
        print(f"❌ 未知命令: {command}")
        show_help()
        return 1


if __name__ == "__main__":
    sys.exit(main())
