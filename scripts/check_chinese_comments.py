#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中文注释格式检查脚本

检查 Python 代码中的中文注释格式是否正确
"""

import re
import sys
from pathlib import Path
from typing import List, <PERSON><PERSON>


def check_chinese_comment_format(file_path: Path) -> List[Tuple[int, str]]:
    """检查单个文件中的中文注释格式"""
    issues = []

    try:
        content = file_path.read_text(encoding="utf-8")
        lines = content.split("\n")

        for line_num, line in enumerate(lines, 1):
            # 跳过空行
            if not line.strip():
                continue

            # 检查是否包含中文
            if re.search(r"[\u4e00-\u9fff]", line):
                # 检查是否是注释行
                stripped_line = line.strip()

                # 检查单行注释格式
                if stripped_line.startswith("#"):
                    # 检查注释符号后是否有空格
                    if not re.match(r"^#\s+", stripped_line):
                        issues.append((line_num, "注释符号 '#' 后应该有空格"))

                    # 检查中文注释是否以句号结尾
                    # chinese_content = stripped_line[1:].strip()
                    # if chinese_content and not chinese_content.endswith(
                    #     ("。", "！", "？", "：", "；")
                    # ):
                    #     # 允许一些特殊情况
                    #     if not any(
                    #         keyword in chinese_content
                    #         for keyword in ["TODO", "FIXME", "NOTE", "WARNING"]
                    #     ):
                    #         issues.append((line_num, "中文注释应该以适当的标点符号结尾"))

                # 检查多行注释中的中文
                # elif '"""' in line or "'''" in line:
                #     # 检查多行注释中的中文格式
                #     if '"""' in line:
                #         docstring_content = re.search(r'"""([^"]*)"', line)
                #         if docstring_content:
                #             content = docstring_content.group(1)
                #             if re.search(r"[\u4e00-\u9fff]", content):
                #                 if not content.strip().endswith(
                #                     ("。", "！", "？", "：", "；")
                #                 ):
                #                     issues.append((line_num, "文档字符串中的中文应该以适当的标点符号结尾"))

                # 检查字符串中的中文（可能是注释）
                # elif '"' in line or "'" in line:
                #     # 检查字符串中的中文是否可能是注释
                #     string_content = re.findall(
                #         r'["\']([^"\']*[\u4e00-\u9fff][^"\']*)["\']', line
                #     )
                #     for content in string_content:
                #         if re.search(r"[\u4e00-\u9fff]", content):
                #             # 如果字符串看起来像注释，检查格式
                #             if any(
                #                 keyword in content
                #                 for keyword in ["说明", "注释", "注意", "提示"]
                #             ):
                #                 if not content.endswith(("。", "！", "？", "：", "；")):
                #                     issues.append((line_num, "字符串中的中文注释应该以适当的标点符号结尾"))

    except Exception as e:
        issues.append((0, f"读取文件时出错: {e}"))

    return issues


def main() -> int:
    """主函数"""
    print("🔍 检查中文注释格式...")

    # 获取当前目录
    current_dir = Path.cwd()

    # 查找所有 Python 文件
    python_files = list(current_dir.rglob("*.py"))

    # 排除一些目录
    excluded_dirs = {".git", "__pycache__", ".pytest_cache", ".venv", "venv", "env"}
    python_files = [
        f
        for f in python_files
        if not any(excluded in f.parts for excluded in excluded_dirs)
    ]

    total_issues = 0

    for file_path in python_files:
        issues = check_chinese_comment_format(file_path)

        if issues:
            print(f"\n📄 {file_path}:")
            for line_num, issue in issues:
                if line_num > 0:
                    print(f"   第 {line_num} 行: {issue}")
                else:
                    print(f"   {issue}")
            total_issues += len(issues)

    if total_issues == 0:
        print("✅ 所有中文注释格式正确")
        return 0
    else:
        print(f"\n❌ 发现 {total_issues} 个中文注释格式问题")
        print("💡 建议:")
        print("   - 注释符号 '#' 后应该有空格")
        print("   - 中文注释应该以适当的标点符号结尾（。！？：；）")
        print("   - 文档字符串中的中文也应该遵循相同的规则")
        return 1


if __name__ == "__main__":
    sys.exit(main())
