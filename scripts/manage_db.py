#!/usr/bin/env python3
# flake8: noqa E402
"""
数据库管理脚本

提供数据库初始化、迁移、状态检查等命令行功能。
"""

import argparse
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import text

from database.manager import db_manager
from database.migrations import migration_manager


async def init_database() -> bool:
    """初始化数据库"""
    print("正在初始化数据库...")
    try:
        await db_manager.initialize()
        print("✅ 数据库初始化成功")

        # 获取数据库信息
        info = await db_manager.get_database_info()
        print("\n数据库信息:")
        for key, value in info.items():
            print(f"  {key}: {value}")

    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return False
    return True


async def check_connection() -> bool:
    """检查数据库连接"""
    print("正在检查数据库连接...")
    try:
        is_connected = await db_manager.check_connection()
        if is_connected:
            print("✅ 数据库连接正常")

            # 获取数据库信息
            info = await db_manager.get_database_info()
            print("\n数据库信息:")
            for key, value in info.items():
                print(f"  {key}: {value}")
        else:
            print("❌ 数据库连接失败")
            return False
    except Exception as e:
        print(f"❌ 连接检查失败: {e}")
        return False
    return True


def init_migrations() -> bool:
    """初始化Alembic迁移环境"""
    print("正在初始化Alembic迁移环境...")
    try:
        migration_manager.init()
        print("✅ Alembic环境初始化成功")
        return True
    except Exception as e:
        print(f"❌ Alembic环境初始化失败: {e}")
        return False


def create_migration(message: str) -> bool:
    """创建新的迁移文件"""
    print(f"正在创建迁移文件: {message}")
    try:
        migration_path = migration_manager.create_migration(message)
        if migration_path:
            print(f"✅ 迁移文件创建成功: {migration_path}")
            return True
        else:
            print("❌ 迁移文件创建失败")
            return False
    except Exception as e:
        print(f"❌ 创建迁移文件失败: {e}")
        return False


def upgrade_database(revision: str = "head") -> bool:
    """升级数据库"""
    print(f"正在升级数据库到版本: {revision}")
    try:
        success = migration_manager.upgrade(revision)
        if success:
            print("✅ 数据库升级成功")
            return True
        else:
            print("❌ 数据库升级失败")
            return False
    except Exception as e:
        print(f"❌ 数据库升级失败: {e}")
        return False


def downgrade_database(revision: str) -> bool:
    """降级数据库"""
    print(f"正在降级数据库到版本: {revision}")
    try:
        success = migration_manager.downgrade(revision)
        if success:
            print("✅ 数据库降级成功")
            return True
        else:
            print("❌ 数据库降级失败")
            return False
    except Exception as e:
        print(f"❌ 数据库降级失败: {e}")
        return False


def show_current_revision() -> bool:
    """显示当前数据库版本"""
    print("正在获取当前数据库版本...")
    try:
        current = migration_manager.current()
        if current:
            print(f"当前版本: {current}")
            return True
        else:
            print("❌ 获取当前版本失败")
            return False
    except Exception as e:
        print(f"❌ 获取当前版本失败: {e}")
        return False


def show_migration_history() -> bool:
    """显示迁移历史"""
    print("正在获取迁移历史...")
    try:
        history = migration_manager.history()
        if history:
            print("迁移历史:")
            for item in history:
                print(f"  {item}")
        else:
            print("暂无迁移历史")
        return True
    except Exception as e:
        print(f"❌ 获取迁移历史失败: {e}")
        return False


async def reset_database() -> bool:
    """重置数据库（危险操作）"""
    print("⚠️  警告：这将删除所有数据！")
    confirm = input("确认要重置数据库吗？(输入 'yes' 确认): ")
    if confirm.lower() != "yes":
        print("操作已取消")
        return False

    print("正在重置数据库...")
    try:
        # 删除所有表
        async with db_manager.async_engine.begin() as conn:
            # 使用异步连接执行 DDL 语句
            # PostgreSQL 不允许在单个 prepared statement 中执行多个命令，需要分开执行
            await conn.execute(text("DROP SCHEMA public CASCADE"))
            await conn.execute(text("CREATE SCHEMA public"))
            # 重新安装 pgvector 扩展
            await conn.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))

        # 重新初始化
        await db_manager.initialize()
        print("✅ 数据库重置成功")
        return True
    except Exception as e:
        print(f"❌ 数据库重置失败: {e}")
        return False


def main() -> None:
    """主函数"""
    parser = argparse.ArgumentParser(description="数据库管理工具")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")

    # 初始化数据库
    init_parser = subparsers.add_parser("init", help="初始化数据库")

    # 检查连接
    check_parser = subparsers.add_parser("check", help="检查数据库连接")

    # 初始化迁移
    init_mig_parser = subparsers.add_parser("init-migrations", help="初始化Alembic迁移环境")

    # 创建迁移
    create_mig_parser = subparsers.add_parser("create-migration", help="创建新的迁移文件")
    create_mig_parser.add_argument("message", help="迁移描述信息")

    # 升级数据库
    upgrade_parser = subparsers.add_parser("upgrade", help="升级数据库")
    upgrade_parser.add_argument("--revision", default="head", help="目标版本")

    # 降级数据库
    downgrade_parser = subparsers.add_parser("downgrade", help="降级数据库")
    downgrade_parser.add_argument("revision", help="目标版本")

    # 显示当前版本
    current_parser = subparsers.add_parser("current", help="显示当前数据库版本")

    # 显示迁移历史
    history_parser = subparsers.add_parser("history", help="显示迁移历史")

    # 重置数据库
    reset_parser = subparsers.add_parser("reset", help="重置数据库（危险操作）")

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    # 执行对应命令
    if args.command == "init":
        asyncio.run(init_database())
    elif args.command == "check":
        asyncio.run(check_connection())
    elif args.command == "init-migrations":
        init_migrations()
    elif args.command == "create-migration":
        create_migration(args.message)
    elif args.command == "upgrade":
        upgrade_database(args.revision)
    elif args.command == "downgrade":
        downgrade_database(args.revision)
    elif args.command == "current":
        show_current_revision()
    elif args.command == "history":
        show_migration_history()
    elif args.command == "reset":
        asyncio.run(reset_database())


if __name__ == "__main__":
    main()
