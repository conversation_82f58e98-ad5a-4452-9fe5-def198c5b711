#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
版本号检查脚本

检查 CHANGELOG.md 和 pyproject.toml 中的版本号是否一致
"""

import re
import sys
from pathlib import Path
from typing import Optional


def extract_version_from_pyproject() -> Optional[str]:
    """从 pyproject.toml 中提取版本号"""
    pyproject_path = Path("pyproject.toml")
    if not pyproject_path.exists():
        print("❌ pyproject.toml 文件不存在")
        return None

    try:
        content = pyproject_path.read_text(encoding="utf-8")
        # 匹配 version = "x.y.z" 格式
        match = re.search(r'version\s*=\s*["\']([^"\']+)["\']', content)
        if match:
            return match.group(1)
        else:
            print("❌ 在 pyproject.toml 中未找到版本号")
            return None
    except Exception as e:
        print(f"❌ 读取 pyproject.toml 时出错: {e}")
        return None


def extract_latest_version_from_changelog() -> Optional[str]:
    """从 CHANGELOG.md 中提取最新版本号"""
    changelog_path = Path("CHANGELOG.md")
    if not changelog_path.exists():
        print("❌ CHANGELOG.md 文件不存在")
        return None

    try:
        content = changelog_path.read_text(encoding="utf-8")
        # 匹配 ## [x.y.z] - yyyy-mm-dd 格式
        match = re.search(
            r"##\s*\[([0-9]+\.[0-9]+\.[0-9]+)\]\s*-\s*\d{4}-\d{2}-\d{2}", content
        )
        if match:
            return match.group(1)
        else:
            print("❌ 在 CHANGELOG.md 中未找到版本号")
            return None
    except Exception as e:
        print(f"❌ 读取 CHANGELOG.md 时出错: {e}")
        return None


def main() -> int:
    """主函数"""
    print("🔍 检查版本号一致性...")

    # 获取 pyproject.toml 中的版本号
    pyproject_version = extract_version_from_pyproject()
    if pyproject_version is None:
        return 1

    # 获取 CHANGELOG.md 中的版本号
    changelog_version = extract_latest_version_from_changelog()
    if changelog_version is None:
        return 1

    print(f"📦 pyproject.toml 版本: {pyproject_version}")
    print(f"📝 CHANGELOG.md 版本: {changelog_version}")

    # 比较版本号
    if pyproject_version == changelog_version:
        print("✅ 版本号一致")
        return 0
    else:
        print("❌ 版本号不一致！")
        print("   请确保 pyproject.toml 和 CHANGELOG.md 中的版本号相同")
        return 1


if __name__ == "__main__":
    sys.exit(main())
