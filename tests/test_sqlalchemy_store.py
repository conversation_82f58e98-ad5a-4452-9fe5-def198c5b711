"""
SQLAlchemy向量存储管理器测试用例

测试文档的写入、查询和删除功能
"""

from datetime import datetime
from typing import List

import pytest
import pytest_asyncio
from langchain_core.documents import Document

from vectorstore.sqlalchemy_store import SQLAlchemyVectorStoreManager


class TestSQLAlchemyVectorStoreManager:
    """SQLAlchemy向量存储管理器测试类"""

    @pytest_asyncio.fixture
    async def vector_store(self) -> SQLAlchemyVectorStoreManager:
        """
        向量存储管理器fixture

        Returns:
            SQLAlchemyVectorStoreManager: 初始化好的向量存储管理器
        """
        # 创建向量存储管理器
        store = SQLAlchemyVectorStoreManager()

        # 初始化向量存储
        await store.initialize()

        # 清理：确保测试前数据库是空的
        await store.clear_all()

        yield store

        # 清理：测试后清空数据并关闭连接
        await store.clear_all()
        await store.close()

    @pytest_asyncio.fixture
    async def sample_documents(self) -> List[Document]:
        """
        示例文档fixture

        Returns:
            List[Document]: 测试用的文档列表
        """
        return [
            Document(
                page_content="人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。",
                metadata={
                    "source": "ai_textbook",
                    "document_type": "textbook",
                    "created_at": datetime(2024, 1, 1, 12, 0, 0),
                    "updated_at": datetime(2024, 1, 1, 12, 0, 0),
                },
            ),
            Document(
                page_content="机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。",
                metadata={
                    "source": "ml_guide",
                    "document_type": "guide",
                    "created_at": datetime(2024, 1, 2, 12, 0, 0),
                    "updated_at": datetime(2024, 1, 2, 12, 0, 0),
                },
            ),
            Document(
                page_content="深度学习是机器学习的一个分支，使用多层神经网络来模拟人脑的学习过程。",
                metadata={
                    "source": "dl_research",
                    "document_type": "research",
                    "created_at": datetime(2024, 1, 3, 12, 0, 0),
                    "updated_at": datetime(2024, 1, 3, 12, 0, 0),
                },
            ),
        ]

    @pytest.mark.asyncio
    async def test_initialize(self, vector_store: SQLAlchemyVectorStoreManager):
        """测试向量存储初始化"""
        # 验证向量存储管理器已正确初始化
        assert vector_store.embedding_manager is not None
        assert vector_store.vector_size > 0
        print("✅ 向量存储初始化测试通过")

    @pytest.mark.asyncio
    async def test_add_documents(
        self,
        vector_store: SQLAlchemyVectorStoreManager,
        sample_documents: List[Document],
    ):
        """测试添加文档功能"""
        # 添加文档
        doc_ids = await vector_store.add_documents(sample_documents)

        # 验证返回的ID数量
        assert len(doc_ids) == len(sample_documents)
        assert all(isinstance(doc_id, str) for doc_id in doc_ids)

        # 验证文档数量
        count = await vector_store.get_document_count()
        assert count == len(sample_documents)

        print(f"✅ 添加文档测试通过，添加了 {len(doc_ids)} 个文档")

    @pytest.mark.asyncio
    async def test_add_texts(self, vector_store: SQLAlchemyVectorStoreManager):
        """测试添加文本功能"""
        texts = ["自然语言处理是人工智能的重要分支。", "计算机视觉使机器能够理解和分析图像。", "语音识别技术让计算机能够理解人类语音。"]

        metadatas = [
            {"source": "nlp_intro", "document_type": "introduction"},
            {"source": "cv_basics", "document_type": "basics"},
            {"source": "speech_tech", "document_type": "technology"},
        ]

        # 添加文本
        doc_ids = await vector_store.add_texts(texts, metadatas)

        # 验证返回的ID数量
        assert len(doc_ids) == len(texts)

        # 验证文档数量
        count = await vector_store.get_document_count()
        assert count == len(texts)

        print(f"✅ 添加文本测试通过，添加了 {len(doc_ids)} 个文本")

    @pytest.mark.asyncio
    async def test_similarity_search(
        self,
        vector_store: SQLAlchemyVectorStoreManager,
        sample_documents: List[Document],
    ):
        """测试相似度搜索功能"""
        # 先添加文档
        await vector_store.add_documents(sample_documents)

        # 执行相似度搜索
        query = "什么是人工智能？"
        results = await vector_store.similarity_search(query, k=2)

        # 验证搜索结果
        assert len(results) <= 2
        assert all(isinstance(doc, Document) for doc in results)

        # 验证文档内容不为空
        for doc in results:
            assert doc.page_content.strip() != ""
            assert "id" in doc.metadata
            assert "source" in doc.metadata
            assert "document_type" in doc.metadata

        print(f"✅ 相似度搜索测试通过，找到 {len(results)} 个相关文档")

    @pytest.mark.asyncio
    async def test_similarity_search_with_score(
        self,
        vector_store: SQLAlchemyVectorStoreManager,
        sample_documents: List[Document],
    ):
        """测试带分数的相似度搜索功能"""
        # 先添加文档
        await vector_store.add_documents(sample_documents)

        # 执行带分数的相似度搜索
        query = "机器学习技术"
        results_with_scores = await vector_store.similarity_search_with_score(
            query, k=3
        )

        # 验证搜索结果
        assert len(results_with_scores) <= 3
        assert all(
            isinstance(item, tuple) and len(item) == 2 for item in results_with_scores
        )

        # 验证分数在合理范围内
        for doc, score in results_with_scores:
            assert isinstance(doc, Document)
            assert isinstance(score, float)
            assert 0.0 <= score <= 1.0  # 相似度分数应该在0-1之间

        print(f"✅ 带分数的相似度搜索测试通过，找到 {len(results_with_scores)} 个相关文档")

    @pytest.mark.asyncio
    async def test_similarity_search_with_filter(
        self,
        vector_store: SQLAlchemyVectorStoreManager,
        sample_documents: List[Document],
    ):
        """测试带过滤条件的相似度搜索"""
        # 先添加文档
        await vector_store.add_documents(sample_documents)

        # 按文档类型过滤搜索
        filter_dict = {"document_type": "textbook"}
        query = "智能系统"
        results = await vector_store.similarity_search(
            query, k=5, filter_dict=filter_dict
        )

        # 验证所有结果都符合过滤条件
        for doc in results:
            assert doc.metadata["document_type"] == "textbook"

        print(f"✅ 带过滤条件的相似度搜索测试通过，找到 {len(results)} 个符合条件的文档")

    @pytest.mark.asyncio
    async def test_get_document_by_id(
        self,
        vector_store: SQLAlchemyVectorStoreManager,
        sample_documents: List[Document],
    ):
        """测试根据ID获取文档"""
        # 先添加文档
        doc_ids = await vector_store.add_documents(sample_documents)

        # 获取第一个文档
        first_doc_id = doc_ids[0]
        doc = await vector_store.get_document_by_id(first_doc_id)

        # 验证文档存在
        assert doc is not None
        assert doc.page_content == sample_documents[0].page_content
        assert doc.metadata["id"] == int(first_doc_id)

        # 测试获取不存在的文档
        non_existent_doc = await vector_store.get_document_by_id("99999")
        assert non_existent_doc is None

        print("✅ 根据ID获取文档测试通过")

    @pytest.mark.asyncio
    async def test_update_document(
        self,
        vector_store: SQLAlchemyVectorStoreManager,
        sample_documents: List[Document],
    ):
        """测试更新文档功能"""
        # 先添加文档
        doc_ids = await vector_store.add_documents(sample_documents)
        first_doc_id = doc_ids[0]

        # 更新文档内容
        new_content = "更新后的人工智能定义：人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的智能系统。"
        new_metadata = {
            "source": "updated_ai_textbook",
            "document_type": "updated_textbook",
        }

        # 执行更新
        success = await vector_store.update_document(
            first_doc_id, new_content, new_metadata
        )
        assert success is True

        # 验证更新结果
        updated_doc = await vector_store.get_document_by_id(first_doc_id)
        assert updated_doc is not None
        assert updated_doc.page_content == new_content
        assert updated_doc.metadata["source"] == new_metadata["source"]
        assert updated_doc.metadata["document_type"] == new_metadata["document_type"]

        # 测试更新不存在的文档
        non_existent_success = await vector_store.update_document("99999", "test", {})
        assert non_existent_success is False

        print("✅ 更新文档测试通过")

    @pytest.mark.asyncio
    async def test_delete_documents(
        self,
        vector_store: SQLAlchemyVectorStoreManager,
        sample_documents: List[Document],
    ):
        """测试删除文档功能"""
        # 先添加文档
        doc_ids = await vector_store.add_documents(sample_documents)
        initial_count = await vector_store.get_document_count()

        # 删除第一个文档
        doc_to_delete = [doc_ids[0]]
        await vector_store.delete_documents(doc_to_delete)

        # 验证文档数量减少
        new_count = await vector_store.get_document_count()
        assert new_count == initial_count - 1

        # 验证文档已被删除
        deleted_doc = await vector_store.get_document_by_id(doc_ids[0])
        assert deleted_doc is None

        print("✅ 删除文档测试通过")

    @pytest.mark.asyncio
    async def test_get_document_count(self, vector_store: SQLAlchemyVectorStoreManager):
        """测试获取文档数量功能"""
        # 初始数量应该为0
        initial_count = await vector_store.get_document_count()
        assert initial_count == 0

        # 添加文档后数量应该增加
        texts = ["测试文档1", "测试文档2"]
        await vector_store.add_texts(texts)

        new_count = await vector_store.get_document_count()
        assert new_count == len(texts)

        print(f"✅ 获取文档数量测试通过，当前文档数量：{new_count}")

    @pytest.mark.asyncio
    async def test_clear_all(
        self,
        vector_store: SQLAlchemyVectorStoreManager,
        sample_documents: List[Document],
    ):
        """测试清空所有文档功能"""
        # 先添加文档
        await vector_store.add_documents(sample_documents)
        initial_count = await vector_store.get_document_count()
        assert initial_count > 0

        # 清空所有文档
        await vector_store.clear_all()

        # 验证所有文档已被清空
        final_count = await vector_store.get_document_count()
        assert final_count == 0

        print("✅ 清空所有文档测试通过")

    @pytest.mark.asyncio
    async def test_integration_workflow(
        self, vector_store: SQLAlchemyVectorStoreManager
    ):
        """测试完整的集成工作流程"""
        # 1. 添加文档
        texts = [
            "Python是一种高级编程语言，以其简洁的语法和强大的功能而闻名。",
            "Django是一个高级的Python Web框架，鼓励快速开发和简洁实用的设计。",
            "Flask是一个轻量级的Python Web框架，易于学习和使用。",
        ]

        metadatas = [
            {"source": "python_docs", "document_type": "language"},
            {"source": "django_docs", "document_type": "framework"},
            {"source": "flask_docs", "document_type": "framework"},
        ]

        doc_ids = await vector_store.add_texts(texts, metadatas)
        assert len(doc_ids) == 3

        # 2. 搜索文档
        query = "Python编程"
        results = await vector_store.similarity_search(query, k=2)
        assert len(results) <= 2

        # 3. 更新文档
        first_doc_id = doc_ids[0]
        await vector_store.update_document(
            first_doc_id, "Python是一种高级编程语言，以其简洁的语法和强大的功能而闻名，广泛应用于数据科学和Web开发。"
        )

        # 4. 验证更新
        updated_doc = await vector_store.get_document_by_id(first_doc_id)
        assert "数据科学" in updated_doc.page_content

        # 5. 删除文档
        await vector_store.delete_documents([doc_ids[1]])
        count_after_delete = await vector_store.get_document_count()
        assert count_after_delete == 2

        # 6. 清空所有文档
        await vector_store.clear_all()
        final_count = await vector_store.get_document_count()
        assert final_count == 0

        print("✅ 完整集成工作流程测试通过")


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
