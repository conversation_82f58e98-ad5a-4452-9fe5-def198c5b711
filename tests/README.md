# 测试说明文档

本目录包含项目的测试用例，主要用于测试SQLAlchemy向量存储管理器的功能。

## 测试文件结构

```
tests/
├── __init__.py                    # 测试包初始化文件
├── README.md                      # 测试说明文档
└── test_sqlalchemy_store.py      # SQLAlchemy向量存储管理器测试用例
```

## 测试用例概览

`test_sqlalchemy_store.py` 包含以下测试用例：

### 基础功能测试

1. **test_initialize** - 测试向量存储初始化
2. **test_add_documents** - 测试添加文档功能
3. **test_add_texts** - 测试添加文本功能
4. **test_get_document_count** - 测试获取文档数量

### 搜索功能测试

5. **test_similarity_search** - 测试相似度搜索功能
6. **test_similarity_search_with_score** - 测试带分数的相似度搜索
7. **test_similarity_search_with_filter** - 测试带过滤条件的搜索

### 文档管理测试

8. **test_get_document_by_id** - 测试根据ID获取文档
9. **test_update_document** - 测试更新文档功能
10. **test_delete_documents** - 测试删除文档功能
11. **test_clear_all** - 测试清空所有文档功能

### 集成测试

12. **test_integration_workflow** - 测试完整的集成工作流程

## 运行测试

### 方法1：使用测试运行脚本（推荐）

```bash
# 运行所有测试
python run_tests.py

# 显示可用测试用例
python run_tests.py --list

# 运行特定测试
python run_tests.py --test TestSQLAlchemyVectorStoreManager::test_initialize
```

### 方法2：直接使用uv和pytest

```bash
# 运行所有测试
uv run pytest tests/ -v

# 运行特定测试文件
uv run pytest tests/test_sqlalchemy_store.py -v

# 运行特定测试用例
uv run pytest tests/test_sqlalchemy_store.py::TestSQLAlchemyVectorStoreManager::test_initialize -v

# 运行带标记的测试
uv run pytest tests/ -m asyncio -v
```

### 方法3：使用pytest直接运行

```bash
# 安装测试依赖
uv add --dev pytest pytest-asyncio pytest-cov

# 运行测试
pytest tests/ -v
```

## 测试配置

测试配置在 `pyproject.toml` 中的 `[tool.pytest.ini_options]` 部分：

- `asyncio_mode = "auto"` - 自动检测异步测试
- `testpaths = ["tests"]` - 测试目录
- `python_files = ["test_*.py"]` - 测试文件模式
- `python_classes = ["Test*"]` - 测试类模式
- `python_functions = ["test_*"]` - 测试函数模式

## 测试标记

项目定义了以下测试标记：

- `@pytest.mark.asyncio` - 异步测试标记
- `@pytest.mark.slow` - 慢速测试标记
- `@pytest.mark.integration` - 集成测试标记

## 测试数据

测试使用中文文档数据，涵盖人工智能、机器学习、深度学习等主题，确保测试的实用性和真实性。

## 注意事项

1. **数据库依赖**：测试需要PostgreSQL数据库和pgvector扩展
2. **异步测试**：所有测试都是异步的，使用pytest-asyncio插件
3. **资源清理**：测试会自动清理测试数据，确保测试环境的干净
4. **环境要求**：需要Python 3.12+和uv包管理器

## 故障排除

### 常见问题

1. **数据库连接失败**
    - 确保PostgreSQL服务正在运行
    - 检查数据库配置是否正确
    - 确保pgvector扩展已安装

2. **模块导入错误**
    - 确保在项目根目录运行测试
    - 检查Python路径设置

3. **异步测试失败**
    - 确保安装了pytest-asyncio插件
    - 检查测试函数是否正确使用async/await

### 调试技巧

```bash
# 显示详细错误信息
pytest tests/ -v --tb=long

# 在第一个失败处停止
pytest tests/ -x

# 显示本地变量
pytest tests/ -l

# 运行特定测试并显示输出
pytest tests/test_sqlalchemy_store.py::TestSQLAlchemyVectorStoreManager::test_initialize -v -s
```
