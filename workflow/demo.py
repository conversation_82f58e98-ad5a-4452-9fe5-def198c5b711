#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/5 10:01
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : demo.py
# @Update  : 2025/8/5 10:01 更新描述
# flake8: noqa E501, F541
from typing import Dict, Optional, TypedDict

# LangChain 和 LangGraph 导入
from langchain_openai import ChatOpenAI
from langgraph.graph import END, StateGraph
from PIL import Image
from pix2tex.cli import LatexOCR

# --- 1. 系统配置与初始化 ---
# 初始化两个不同的大语言模型
# 模型 A：解答专家 - 使用 Google Gemini，可以设置一些创造性 (temperature > 0)
# 确保您已设置 GOOGLE_API_KEY 环境变量
try:
    # solver_llm = ChatOllama(
    #     model="qwen3:4b", temperature=0.5,
    # )
    solver_llm = ChatOpenAI(
        model="qwen3-0.6b",
        temperature=0.5,
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        api_key="sk-23aabb46439c44d6be323128d0dc1ab3",
    )
except Exception as e:
    print(f"初始化 Gemini 模型时出错: {e}。请检查您的 GOOGLE_API_KEY。")
    exit()

# 模型 B：审计专家 - 使用 OpenAI GPT-4o，设置为零创造性以保证严格性
# 确保您已设置 OPENAI_API_KEY 环境变量
try:
    auditor_llm = ChatOpenAI(
        model="qwen3-30b-a3b-instruct-2507",
        temperature=0.0,
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        api_key="sk-23aabb46439c44d6be323128d0dc1ab3",
    )
except Exception as e:
    print(f"初始化 OpenAI 模型时出错: {e}。请检查您的 OPENAI_API_KEY。")
    exit()

# 定义重试次数上限，防止无限循环
MAX_RETRIES = 2


# --- 2. 定义智能体状态 ---


class AgentState(TypedDict):
    """
    定义在图中节点之间传递的状态。
    """

    input_content: str  # 输入：图片文件路径、文本题目或 LaTeX 公式
    input_type: str  # 输入类型：image、text 或 latex
    user_profile: Dict  # 输入：用户画像，例如 {'education': '初中', 'age': 14}
    problem_text: str  # 识别器的输出
    generated_response: str  # 解答专家的输出
    audit_critique: Optional[str]  # 审计员的输出（如果发现问题）
    retry_count: int  # 重试循环的计数器
    final_response: str  # 最终经过审计的答案，呈现给用户


# --- 3. 定义节点函数 ---


def recognize_problem_input(state: AgentState) -> AgentState:
    """
    节点 1：根据输入类型处理不同格式的题目输入。
    支持图片文件（OCR识别）、直接文本和LaTeX公式。
    """
    print("--- 任务开始: 题目识别 ---")

    input_content = state["input_content"]
    input_type = state["input_type"]

    try:
        if input_type == "image":
            # 处理图片文件，使用OCR识别
            print("处理图片输入，使用OCR识别...")
            image = Image.open(input_content)
            # 使用 OCR 提取文本，指定中文和英文语言
            model = LatexOCR()
            # problem_text = pytesseract.image_to_string(image, lang='chi_sim+eng')
            # cleaned_text = problem_text.strip()
            cleaned_text = model(image)
            print(f"识别出的题目文本: {cleaned_text}")

        elif input_type == "text":
            # 处理直接文本输入
            print("处理文本输入...")
            cleaned_text = input_content.strip()
            print(f"接收到的文本题目: {cleaned_text}")

        elif input_type == "latex":
            # 处理LaTeX格式输入
            print("处理LaTeX格式输入...")
            cleaned_text = input_content.strip()
            print(f"接收到的LaTeX公式: {cleaned_text}")

        else:
            raise ValueError(f"不支持的输入类型: {input_type}")

        state["problem_text"] = cleaned_text

    except FileNotFoundError:
        print(f"错误: 无法找到图片文件 '{input_content}'。")
        state["problem_text"] = "图像文件未找到。"
    except Exception as e:
        print(f"题目识别失败: {e}")
        if input_type == "image":
            state["problem_text"] = "图像识别失败，请检查文件内容或 Tesseract 安装。"
        else:
            state["problem_text"] = f"输入处理失败: {str(e)}"

    return state


def solver_explainer_node(state: AgentState) -> AgentState:
    """
    节点 2：解答专家。生成详细的解答和解释。
    如果提供了批评意见，它将尝试修正答案。
    """
    print("--- 任务开始: 解答生成 ---")

    critique = state.get("audit_critique")
    profile = state["user_profile"]

    if critique:
        # 这是重试尝试
        print(f"收到审计意见，正在进行第 {state['retry_count']} 次修正...")
        prompt_template = f"""
        你是一位资深的数学老师。你之前解答问题的尝试被发现存在错误。
        请根据以下批评意见修正你的答案，并生成一个新的、完整且准确的回答。

        **审计员的批评意见：**
        {critique}

        **原始问题：**
        {state['problem_text']}

        你的任务是为一名学生生成修正后的完整解答，该学生的情况是：{profile['education']}（大约 {profile['age']} 岁）。
        解答必须包含详细步骤和所有相关概念的清晰解释。
        """
    else:
        # 这是第一次尝试
        prompt_template = f"""
        你是一位资深的数学老师。你的任务是为一名学生解答数学问题，该学生的情况是：{profile['education']}（大约 {profile['age']} 岁）。
        请创建一个包含两个部分的全面回答：
        1.  **解题步骤：** 提供详细的、分步骤的问题解答，确保所有计算准确且逻辑易于理解。
        2.  **概念解释：** 在解答后，详细解释使用的每个关键数学概念，使用适合学生水平的语言。

        **题目是：**
        {state['problem_text']}
        """  # ignore

    # 使用流式调用生成响应
    print("正在生成解答...")
    response_chunks = []

    try:
        for chunk in solver_llm.stream(prompt_template):
            content = chunk.content
            if content:
                print(content, end="", flush=True)
                response_chunks.append(content)

        print("\n解答生成完成")
        response = "".join(response_chunks)

    except Exception as e:
        print(f"\n流式调用失败，回退到普通调用: {e}")
        response = solver_llm.invoke(prompt_template).content

    state["generated_response"] = response
    state["retry_count"] = state.get("retry_count", 0) + 1
    return state


def auditor_node(state: AgentState) -> AgentState:
    """
    节点 3：审计专家。严格审查解答专家的回答。
    """
    print("--- 任务开始: 答案审计 ---")

    problem = state["problem_text"]
    response_to_audit = state["generated_response"]

    audit_prompt = f"""
    你是一位严谨的数学家和事实核查员。你的工作是严格审查以下数学问题的解答。

    **原始问题：**
    "{problem}"

    **待审查的解答：**
    ---
    {response_to_audit}
    ---

    请从以下角度审查解答：
    1.  **数学准确性：** 最终答案和每个中间计算是否正确？
    2.  **逻辑严谨性：** 解题步骤是否逻辑正确且应用得当？
    3.  **概念清晰性：** 对概念的解释是否准确？

    **你的回答：**
    - 如果解答完美且完全准确，请只回答 "Pass"。
    - 如果有任何错误，请回答 "Fail"，然后换行，接着详细逐点说明每个错误以及你的修正建议。
    """

    # 使用流式调用进行审计
    print("正在进行审计...")
    critique_chunks = []

    try:
        for chunk in auditor_llm.stream(audit_prompt):
            content = chunk.content
            if content:
                print(content, end="", flush=True)
                critique_chunks.append(content)

        print("\n审计完成")
        critique_response = "".join(critique_chunks).strip()

    except Exception as e:
        print(f"\n流式调用失败，回退到普通调用: {e}")
        critique_response = auditor_llm.invoke(audit_prompt).content.strip()

    if critique_response.startswith("Pass"):
        print("审计结果: Pass. 答案准确无误。")
        state["audit_critique"] = None
        state["final_response"] = response_to_audit  # 答案通过审核
    else:
        # 提取 "Fail" 后的批评文本
        critique_text = critique_response.replace("Fail", "").strip()
        print(f"审计结果: Fail. 发现错误:\n{critique_text}")
        state["audit_critique"] = critique_text

    return state


# --- 4. 定义条件分支 ---


def should_retry(state: AgentState) -> str:
    """
    决策节点：判断是重试解答专家还是结束流程。
    """
    print("--- 任务决策 ---")

    critique = state.get("audit_critique")
    retry_count = state.get("retry_count", 0)

    if critique and retry_count < MAX_RETRIES:
        print(f"决策: 发现错误，将进行重试 (尝试次数: {retry_count}/{MAX_RETRIES})")
        return "retry"
    else:
        if critique:  # 重试次数已用尽但仍有错误
            print(f"决策: 重试次数已达上限但仍存在错误，终止流程。")
            state["final_response"] = "抱歉，系统在多次尝试后仍无法生成一个完全准确的答案。请您检查问题或稍后再试。"
        else:  # 没有批评意见意味着答案是好的
            print("决策: 答案已通过审计，流程结束。")
        return "end"


# --- 5. 构建并编译图 ---

# 实例化图
workflow = StateGraph(AgentState)

# 添加节点
workflow.add_node("recognizer", recognize_problem_input)
workflow.add_node("solver", solver_explainer_node)
workflow.add_node("auditor", auditor_node)

# 设置入口点并构建图的边
workflow.set_entry_point("recognizer")
workflow.add_edge("recognizer", "solver")
workflow.add_edge("solver", "auditor")

# 为重试循环添加条件边
workflow.add_conditional_edges(
    "auditor",
    should_retry,
    {"retry": "solver", "end": END},  # 如果需要重试，返回到解答专家  # 如果不需要，结束流程
)

# 将图编译为可运行的应用程序
app = workflow.compile()

# --- 6. 执行智能体并查看结果 ---

if __name__ == "__main__":
    print("开始执行AI数学教辅智能体...")

    # 示例1: 图片输入（向后兼容）
    inputs_image = {
        "input_content": "image_312228.png",
        "input_type": "image",
        "user_profile": {"education": "初中", "age": 14},
        "retry_count": 0,
    }

    # 示例2: 文本输入
    inputs_text = {
        "input_content": "求解方程 2x + 5 = 13",
        "input_type": "text",
        "user_profile": {"education": "初中", "age": 14},
        "retry_count": 0,
    }

    # 示例3: LaTeX输入
    inputs_latex = {
        "input_content": "\\frac{d}{dx}(x^2 + 3x + 2) = ?",
        "input_type": "latex",
        "user_profile": {"education": "高中", "age": 17},
        "retry_count": 0,
    }

    # 示例4: 自动检测输入类型的辅助函数
    def detect_input_type(input_content: str) -> str:
        """
        自动检测输入类型的辅助函数

        Args:
            input_content: 输入内容

        Returns:
            检测到的输入类型: "image", "text", 或 "latex"
        """
        # 检查是否为图片文件路径
        image_extensions = (".png", ".jpg", ".jpeg", ".gif", ".bmp", ".tiff", ".webp")
        if input_content.lower().endswith(image_extensions):
            return "image"

        # 检查是否包含LaTeX标记
        latex_indicators = ["\\", "$", "\\frac", "\\sqrt", "\\sum", "\\int", "\\lim"]
        if any(indicator in input_content for indicator in latex_indicators):
            return "latex"

        # 默认为文本类型
        return "text"

    def create_inputs_with_auto_detection(content: str, profile: Dict) -> Dict:
        """创建带自动类型检测的输入"""
        return {
            "input_content": content,
            "input_type": detect_input_type(content),
            "user_profile": profile,
            "retry_count": 0,
        }

    # 使用自动检测的示例
    auto_inputs_examples = [
        create_inputs_with_auto_detection(
            "image_312228.png", {"education": "初中", "age": 14}
        ),
        create_inputs_with_auto_detection(
            "求解方程 3x - 7 = 8", {"education": "初中", "age": 14}
        ),
        create_inputs_with_auto_detection(
            "\\int x^2 dx", {"education": "高中", "age": 17}
        ),
    ]

    # 选择要使用的输入示例（这里使用文本输入作为演示）
    inputs = inputs_latex

    print(f"使用输入类型: {inputs['input_type']}")
    print(f"输入内容: {inputs['input_content']}")

    # 执行工作流
    final_state = None
    for s in app.stream(inputs):
        final_state = s

    # 从最后状态中提取并打印最终的、经过审计的回答
    final_response = final_state[next(reversed(final_state))]["final_response"]  # type: ignore[index,arg-type]

    print("\n" + "=" * 50)
    print("          最终解答 (已通过审计)          ")
    print("=" * 50 + "\n")
    print(final_response)
