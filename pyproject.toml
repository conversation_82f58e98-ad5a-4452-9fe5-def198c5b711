[project]
name = "langgraph-demo"
version = "1.3.0"
description = "Add your description here"
requires-python = ">=3.12"
dependencies = [
    "greenlet>=3.2.3",
    "langchain-core>=0.3.72",
    "langchain-ollama>=0.3.6",
    "langchain-openai>=0.3.28",
    "langchain-postgres>=0.0.15",
    "pydantic-settings>=2.10.1",
    "PyYAML>=6.0.1",
    "sqlalchemy>=2.0.0",
    "alembic>=1.13.0",
    "asyncpg>=0.29.0",
    "psycopg2-binary>=2.9.0",
    "pgvector>=0.2.0",
    "gradio>=5.39.0",
    "langgraph>=0.6.3",
    "pytesseract>=0.3.13",
    "pix2tex>=0.1.4",
]

[project.optional-dependencies]
test = [
    "pytest>=8.2.0",
    "pytest-asyncio>=0.23.0",
    "pytest-cov>=4.1.0",
]

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "-v",
    "--tb=short",
    "--strict-markers",
    "--disable-warnings",
]
markers = [
    "asyncio: marks tests as async (deselect with '-m \"not asyncio\"')",
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
]

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_unreachable = true
strict_equality = true
check_untyped_defs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
ignore_missing_imports = true
# 禁用 import-untyped 错误代码
disable_error_code = ["import-untyped"]
# 暂时禁用所有错误代码，以便 pre-commit 能够通过
# disable_error_code = ["import-untyped", "no-untyped-def", "misc", "no-any-return", "union-attr", "attr-defined", "annotation-unchecked"]

# 针对特定模块的配置
[[tool.mypy.overrides]]
module = "tests.*"
allow_untyped_defs = true
allow_untyped_calls = true
disable_error_code = ["no-untyped-def", "misc", "no-any-return", "union-attr"]

[[tool.mypy.overrides]]
module = "examples.*"
allow_untyped_defs = true
allow_untyped_calls = true
disable_error_code = ["no-untyped-def", "misc", "no-any-return", "union-attr"]

[[tool.mypy.overrides]]
module = "scripts.*"
allow_untyped_defs = true
allow_untyped_calls = true
disable_error_code = ["no-untyped-def", "misc", "no-any-return", "union-attr", "attr-defined"]

[[tool.mypy.overrides]]
module = "alembic.*"
ignore_missing_imports = true
disable_error_code = ["attr-defined", "no-untyped-def", "misc", "no-any-return", "union-attr"]

[[tool.mypy.overrides]]
module = "database.*"
ignore_missing_imports = true
disable_error_code = ["attr-defined", "no-untyped-def", "misc", "no-any-return", "union-attr"]

[[tool.mypy.overrides]]
module = "config.*"
ignore_missing_imports = true
disable_error_code = ["import-untyped", "no-untyped-def", "misc", "no-any-return", "union-attr", "attr-defined"]

[[tool.mypy.overrides]]
module = "vectorstore.*"
ignore_missing_imports = true
disable_error_code = ["import-untyped", "no-untyped-def", "misc", "no-any-return", "union-attr", "attr-defined"]

[[tool.mypy.overrides]]
module = "main"
ignore_missing_imports = true
disable_error_code = ["import-untyped", "no-untyped-def", "misc", "no-any-return", "union-attr", "attr-defined"]

[dependency-groups]
dev = [
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
    "pytest-cov>=6.2.1",
    "pre-commit>=3.6.0",
    "black>=23.12.1",
    "isort>=5.13.2",
    "flake8>=7.0.0",
    "mypy>=1.8.0",
    "bandit>=1.7.5",
    "pydocstyle>=6.3.0",
]
