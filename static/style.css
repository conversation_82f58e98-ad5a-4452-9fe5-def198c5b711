/* Streamlit数学教辅智能体自定义样式 */

/* 主要容器样式 */
.main .block-container {
    padding-top: 2rem;
    padding-bottom: 2rem;
    max-width: 1200px;
}

/* 标题样式 */
.main h1 {
    color: #1f77b4;
    border-bottom: 2px solid #1f77b4;
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
}

/* 子标题样式 */
.main h2 {
    color: #2c3e50;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

/* 侧边栏样式 */
.css-1d391kg {
    background-color: #f8f9fa;
}

.css-1d391kg .css-1v0mbdj {
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 按钮样式 */
.stButton > button {
    border-radius: 0.5rem;
    border: none;
    padding: 0.5rem 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.stButton > button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* 主要按钮样式 */
.stButton > button[kind="primary"] {
    background: linear-gradient(45deg, #1f77b4, #2e86ab);
    color: white;
}

.stButton > button[kind="primary"]:hover {
    background: linear-gradient(45deg, #1a6ba0, #2a7a9a);
}

/* 输入框样式 */
.stTextInput > div > div > input,
.stTextArea > div > div > textarea {
    border-radius: 0.5rem;
    border: 2px solid #e1e5e9;
    transition: border-color 0.3s ease;
}

.stTextInput > div > div > input:focus,
.stTextArea > div > div > textarea:focus {
    border-color: #1f77b4;
    box-shadow: 0 0 0 0.2rem rgba(31, 119, 180, 0.25);
}

/* 选择框样式 */
.stSelectbox > div > div {
    border-radius: 0.5rem;
}

/* 滑块样式 */
.stSlider > div > div > div {
    background-color: #1f77b4;
}

/* 进度条样式 */
.stProgress > div > div > div {
    background: linear-gradient(90deg, #1f77b4, #2e86ab);
    border-radius: 1rem;
}

/* 信息框样式 */
.stInfo {
    background-color: #e3f2fd;
    border-left: 4px solid #1f77b4;
    border-radius: 0.5rem;
    padding: 1rem;
}

.stSuccess {
    background-color: #e8f5e8;
    border-left: 4px solid #4caf50;
    border-radius: 0.5rem;
    padding: 1rem;
}

.stWarning {
    background-color: #fff3e0;
    border-left: 4px solid #ff9800;
    border-radius: 0.5rem;
    padding: 1rem;
}

.stError {
    background-color: #ffebee;
    border-left: 4px solid #f44336;
    border-radius: 0.5rem;
    padding: 1rem;
}

/* 展开器样式 */
.streamlit-expanderHeader {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    border: 1px solid #e1e5e9;
}

.streamlit-expanderContent {
    border: 1px solid #e1e5e9;
    border-top: none;
    border-radius: 0 0 0.5rem 0.5rem;
    padding: 1rem;
}

/* 标签页样式 */
.stTabs [data-baseweb="tab-list"] {
    gap: 0.5rem;
}

.stTabs [data-baseweb="tab"] {
    border-radius: 0.5rem 0.5rem 0 0;
    padding: 0.5rem 1rem;
    background-color: #f8f9fa;
    border: 1px solid #e1e5e9;
}

.stTabs [aria-selected="true"] {
    background-color: #1f77b4;
    color: white;
    border-color: #1f77b4;
}

/* 代码块样式 */
.stCode {
    border-radius: 0.5rem;
    border: 1px solid #e1e5e9;
}

/* 数学公式输出样式 */
.math-output {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
    border: 1px solid #e1e5e9;
    font-family: 'Times New Roman', serif;
}

/* 文件上传器样式 */
.stFileUploader > div {
    border-radius: 0.5rem;
    border: 2px dashed #1f77b4;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
}

.stFileUploader > div:hover {
    border-color: #2e86ab;
    background-color: #f8f9fa;
}

/* 图片显示样式 */
.stImage > img {
    border-radius: 0.5rem;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* 列布局样式 */
.css-1r6slb0 {
    gap: 2rem;
}

/* JSON显示样式 */
.stJson {
    border-radius: 0.5rem;
    border: 1px solid #e1e5e9;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main .block-container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .css-1r6slb0 {
        gap: 1rem;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.stMarkdown, .stText, .stCode {
    animation: fadeIn 0.5s ease-out;
}

/* 加载动画 */
.stSpinner > div {
    border-color: #1f77b4 transparent transparent transparent;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #1f77b4;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #2e86ab;
}
