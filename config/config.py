#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/2 15:14
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : config.py
# @Update  : 2025/8/2 15:14 更新描述
"""
配置管理模块

管理系统的各种配置，包括数据库连接、模型设置、API密钥等。
使用 pydantic-settings 实现，支持从 YAML 文件自动加载配置。
"""

from pathlib import Path
from typing import Any, List, Optional

import yaml
from pydantic import BaseModel, Field
from pydantic.fields import FieldInfo
from pydantic_settings import (
    BaseSettings,
    PydanticBaseSettingsSource,
    SettingsConfigDict,
)


class YamlConfigSettingsSource(PydanticBaseSettingsSource):
    """
    YAML 配置文件设置源

    从 YAML 文件中加载配置，支持嵌套结构。
    """

    def __init__(
        self, settings_cls: type[BaseSettings], yaml_file: str = "config.yaml"
    ):
        """初始化 YAML 配置设置源。

        Args:
            settings_cls: 设置类
            yaml_file: YAML 配置文件路径
        """
        super().__init__(settings_cls)
        self.yaml_file = yaml_file

    def get_field_value(
        self, field: FieldInfo, field_name: str
    ) -> tuple[Any, str, bool]:
        """获取字段值"""
        try:
            yaml_path = Path(self.yaml_file)
            if not yaml_path.exists():
                return None, field_name, False

            with open(yaml_path, "r", encoding="utf-8") as f:
                yaml_data = yaml.safe_load(f)

            # 支持嵌套字段访问，如 database.host
            field_value = self._get_nested_value(yaml_data, field_name)
            return field_value, field_name, False

        except Exception as e:
            print(f"警告：加载 YAML 配置文件时出错: {e}")
            return None, field_name, False

    def _get_nested_value(self, data: dict, field_name: str) -> Any:
        """获取嵌套字段值"""
        if "." not in field_name:
            return data.get(field_name)

        keys = field_name.split(".")
        current = data

        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None

        return current

    def prepare_field_value(
        self, field_name: str, field: FieldInfo, value: Any, value_is_complex: bool
    ) -> Any:
        """准备字段值"""
        return value

    def __call__(self) -> dict[str, Any]:
        """调用方法，返回配置字典"""
        d: dict[str, Any] = {}

        for field_name, field in self.settings_cls.model_fields.items():
            field_value, field_key, value_is_complex = self.get_field_value(
                field, field_name
            )
            field_value = self.prepare_field_value(
                field_name, field, field_value, value_is_complex
            )
            if field_value is not None:
                d[field_key] = field_value

        return d


class DatabaseConfig(BaseModel):
    """数据库配置"""

    host: str = Field(default="localhost", description="数据库主机地址")
    port: int = Field(default=5432, description="数据库端口")
    database: str = Field(default="agentic_math", description="数据库名称")
    username: str = Field(default="postgres", description="数据库用户名")
    password: str = Field(default="", description="数据库密码")
    db_schema: str = Field(default="public", description="数据库模式")
    echo: bool = Field(default=False, description="是否输出SQL语句")

    @property
    def connection_string(self) -> str:
        """获取数据库连接字符串"""
        return (
            f"postgresql+asyncpg://{self.username}:{self.password}"
            f"@{self.host}:{self.port}/{self.database}"
        )

    @property
    def sync_connection_string(self) -> str:
        """获取同步数据库连接字符串"""
        return (
            f"postgresql+psycopg://{self.username}:{self.password}"
            f"@{self.host}:{self.port}/{self.database}"
        )


class ModelConfig(BaseModel):
    """模型配置"""

    # Ollama配置
    ollama_base_url: str = Field(
        default="http://localhost:11434", description="Ollama服务地址"
    )
    ollama_model: str = Field(default="llama3.1:8b", description="Ollama模型名称")
    ollama_embedding_model: str = Field(
        default="llama3.1:8b", description="Ollama嵌入模型名称"
    )

    # OpenAI配置（备用）
    openai_api_key: Optional[str] = Field(default=None, description="OpenAI API密钥")
    openai_model: str = Field(default="gpt-4o-mini", description="OpenAI模型名称")
    openai_embedding_model: str = Field(
        default="text-embedding-3-small", description="OpenAI嵌入模型名称"
    )

    # 模型参数
    temperature: float = Field(default=0.1, description="模型温度参数")
    max_tokens: int = Field(default=4096, description="最大token数")
    top_p: float = Field(default=0.9, description="Top-p参数")


class VectorStoreConfig(BaseModel):
    """向量存储配置"""

    collection_name: str = Field(default="knowledge_base", description="向量集合名称")
    vector_size: int = Field(default=4096, description="向量维度大小")
    similarity_threshold: float = Field(default=0.7, description="相似度阈值")
    max_results: int = Field(default=5, description="最大检索结果数")


class DistillationConfig(BaseModel):
    """知识蒸馏配置"""

    # 搜索配置
    search_enabled: bool = Field(default=True, description="是否启用网络搜索")
    search_provider: str = Field(default="tavily", description="搜索提供商")
    max_search_results: int = Field(default=10, description="最大搜索结果数")

    # 内容处理配置
    chunk_size: int = Field(default=1000, description="文本分块大小")
    chunk_overlap: int = Field(default=200, description="文本分块重叠大小")
    max_content_length: int = Field(default=50000, description="最大内容长度")

    # 蒸馏配置
    distillation_model: str = Field(default="llama3.1:8b", description="蒸馏模型名称")
    distillation_temperature: float = Field(default=0.3, description="蒸馏温度参数")
    distillation_max_tokens: int = Field(default=8192, description="蒸馏最大token数")
    distillation_batch_size: int = Field(default=5, description="蒸馏批处理大小")


class CeleryConfig(BaseModel):
    """Celery配置"""

    broker_url: str = Field(default="redis://localhost:6379/0", description="消息代理URL")
    result_backend: str = Field(
        default="redis://localhost:6379/0", description="结果后端URL"
    )
    task_serializer: str = Field(default="json", description="任务序列化格式")
    result_serializer: str = Field(default="json", description="结果序列化格式")
    accept_content: str = Field(default="json", description="接受的内容类型")

    @property
    def accept_content_list(self) -> List[str]:
        """获取接受内容类型列表"""
        return [item.strip() for item in self.accept_content.split(",")]

    timezone: str = Field(default="Asia/Shanghai", description="时区")
    enable_utc: bool = Field(default=True, description="启用UTC")


class Config(BaseSettings):
    """主配置类"""

    model_config = SettingsConfigDict(case_sensitive=False, extra="ignore")

    # 环境配置
    environment: str = Field(default="development", description="运行环境")
    debug: bool = Field(default=False, description="调试模式")
    log_level: str = Field(default="INFO", description="日志级别")

    # 子配置
    database: DatabaseConfig = Field(
        default_factory=DatabaseConfig, description="数据库配置"
    )
    model: ModelConfig = Field(default_factory=ModelConfig, description="模型配置")
    vector_store: VectorStoreConfig = Field(
        default_factory=VectorStoreConfig, description="向量存储配置"
    )
    distillation: DistillationConfig = Field(
        default_factory=DistillationConfig, description="知识蒸馏配置"
    )
    celery: CeleryConfig = Field(default_factory=CeleryConfig, description="Celery配置")

    @classmethod
    def settings_customise_sources(
        cls,
        settings_cls: type[BaseSettings],
        init_settings: PydanticBaseSettingsSource,
        env_settings: PydanticBaseSettingsSource,
        dotenv_settings: PydanticBaseSettingsSource,
        file_secret_settings: PydanticBaseSettingsSource,
    ) -> tuple[PydanticBaseSettingsSource, ...]:
        """自定义配置源优先级"""
        return (
            YamlConfigSettingsSource(settings_cls),
            env_settings,
            init_settings,
            file_secret_settings,
        )


# 全局配置实例
config = Config()
