#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/2 15:18
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : logging_handler.py
# @Update  : 2025/8/2 15:18 更新描述

import logging
import logging.handlers
import os
import sys
from typing import Optional


class UnifiedLoggingHandler:
    """统一日志处理器 - 所有模块共享同一个日志文件"""

    def __init__(
        self,
        app_name: str = "app",
        log_dir: Optional[str] = None,
        level: int = logging.INFO,
    ):
        """
        初始化统一日志处理器

        Args:
            app_name: 应用名称，用于日志文件命名
            log_dir: 日志文件目录，默认为项目根目录下的logs目录
            level: 日志级别
        """
        self.app_name = app_name
        # 如果未指定日志目录，则使用项目根目录下的logs目录
        if log_dir is None:
            # 获取当前文件所在目录的上级目录（项目根目录）
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(current_dir)
            self.log_dir = os.path.join(project_root, "logs")
        else:
            self.log_dir = log_dir
        self.level = level

        # 创建日志目录
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)

        # 配置标准库日志
        self._setup_standard_logging()

    def _setup_standard_logging(self):
        """设置标准库日志配置"""
        # 创建根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(self.level)

        # 清除现有的处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(self.level)
        console_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)

        # 统一日志文件处理器 - 按日期轮转
        log_file = os.path.join(self.log_dir, f"{self.app_name}.log")
        file_handler = logging.handlers.TimedRotatingFileHandler(
            filename=log_file,
            when="midnight",
            interval=1,
            backupCount=30,
            encoding="utf-8",
        )
        file_handler.setLevel(self.level)
        file_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s - %(message)s"  # noqa: E501
        )
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)

        # 错误日志处理器 - 只记录错误级别以上的日志
        error_log_file = os.path.join(self.log_dir, f"{self.app_name}_error.log")
        error_handler = logging.handlers.TimedRotatingFileHandler(
            filename=error_log_file,
            when="midnight",
            interval=1,
            backupCount=30,
            encoding="utf-8",
        )
        error_handler.setLevel(logging.ERROR)
        error_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s - %(message)s"  # noqa: E501
        )
        error_handler.setFormatter(error_formatter)
        root_logger.addHandler(error_handler)

    def get_logger(self, name: Optional[str] = None) -> logging.Logger:
        """
        获取日志器实例

        Args:
            name: 日志器名称，如果为None则使用调用模块的名称

        Returns:
            logging.Logger: 日志器实例
        """
        if name is None:
            # 获取调用者的模块名称
            import inspect

            frame = inspect.currentframe().f_back
            if frame:
                module_name = frame.f_globals.get("__name__", "unknown")
                # 如果是__main__，则使用应用名称
                if module_name == "__main__":
                    name = self.app_name
                else:
                    name = module_name
            else:
                name = self.app_name

        return logging.getLogger(name)


# 全局日志实例
_unified_handler: Optional[UnifiedLoggingHandler] = None


def get_logger(
    name: Optional[str] = None,
    app_name: str = "app",
    log_dir: Optional[str] = None,
    level: int = logging.INFO,
) -> logging.Logger:
    """
    获取全局日志器 - 统一日志管理

    Args:
        name: 日志器名称，如果为None则自动检测
        app_name: 应用名称，用于日志文件命名
        log_dir: 日志文件目录，默认为项目根目录下的logs目录
        level: 日志级别

    Returns:
        logging.Logger: 日志器实例
    """
    global _unified_handler

    # 如果还没有初始化，则创建统一日志处理器
    if _unified_handler is None:
        _unified_handler = UnifiedLoggingHandler(app_name, log_dir, level)

    return _unified_handler.get_logger(name)


logger = get_logger()


def configure_logging(
    app_name: str = "app", log_dir: Optional[str] = None, level: int = logging.INFO
) -> None:
    """
    配置全局日志系统

    Args:
        app_name: 应用名称
        log_dir: 日志文件目录
        level: 日志级别
    """
    global _unified_handler
    _unified_handler = UnifiedLoggingHandler(app_name, log_dir, level)


# 使用示例
if __name__ == "__main__":
    # 配置日志系统
    configure_logging("test_app", level=logging.DEBUG)

    # 获取不同模块的日志器
    main_logger = get_logger("main")
    db_logger = get_logger("database")
    api_logger = get_logger("api")

    # 测试日志输出 - 所有日志都会输出到同一个文件
    main_logger.debug("主模块调试信息")
    main_logger.info("主模块信息")

    db_logger.warning("数据库模块警告")
    db_logger.error("数据库模块错误")

    api_logger.critical("API模块严重错误")

    try:
        1 / 0
    except Exception as e:
        main_logger.exception("发生异常: %s", str(e))
