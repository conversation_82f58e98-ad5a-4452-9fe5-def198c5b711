# =========================
# 环境配置
# =========================
environment: development
debug: true
log_level: INFO

# =========================
# 数据库配置
# =========================
database:
  host: localhost
  port: 5433
  database: agentic_math
  username: mathgraphmind
  password: password
  db_schema: public

# =========================
# Ollama配置
# =========================
model:
  ollama_base_url: http://localhost:11434
  ollama_model: qwen3:0.6b
  ollama_embedding_model: qwen3:0.6b

  # OpenAI配置（备用）
  openai_api_key: your_openai_api_key_here
  openai_model: gpt-4o-mini
  openai_embedding_model: text-embedding-3-small

  # 模型参数
  temperature: 0.1
  max_tokens: 4096
  top_p: 0.9

# =========================
# 向量存储配置
# =========================
vector_store:
  collection_name: knowledge_base
  vector_size: 1024
  similarity_threshold: 0.7
  max_results: 5

# =========================
# 知识蒸馏配置
# =========================
distillation:
  # 搜索配置
  search_enabled: true
  search_provider: tavily
  max_search_results: 10

  # 内容处理配置
  chunk_size: 1000
  chunk_overlap: 200
  max_content_length: 50000

  # 蒸馏配置
  distillation_model: qwen3:0.6b
  distillation_temperature: 0.3
  distillation_max_tokens: 8192
  distillation_batch_size: 5

# =========================
# Tavily搜索配置
# =========================
tavily_api_key: your_tavily_api_key_here

# =========================
# Celery配置
# =========================
celery:
  broker_url: redis://localhost:6379/0
  result_backend: redis://localhost:6379/0
  task_serializer: json
  result_serializer: json
  accept_content: json
  timezone: Asia/Shanghai
  enable_utc: true
