"""重构模型：添加通用字段混入类

Revision ID: 6e72418bc522
Revises: 099147f53a17
Create Date: 2025-08-02 16:39:26.127838

"""
import sqlalchemy as sa

from alembic import op  # type: ignore[attr-defined]

# revision identifiers, used by Alembic.
revision = "6e72418bc522"
down_revision = "099147f53a17"
branch_labels = None
depends_on = None


def upgrade() -> None:
    """升级数据库结构，重构模型并添加通用字段混入类。

    创建 users 表，并为 vector_documents 表添加通用字段注释。
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "users",
        sa.Column("username", sa.String(length=50), nullable=False, comment="用户名"),
        sa.Column("email", sa.String(length=100), nullable=False, comment="邮箱地址"),
        sa.Column("is_active", sa.<PERSON>(), nullable=False, comment="是否激活"),
        sa.<PERSON>umn(
            "id", sa.Integer(), autoincrement=True, nullable=False, comment="主键ID"
        ),
        sa.<PERSON>umn("created_at", sa.DateTime(), nullable=False, comment="创建时间"),
        sa.Column("updated_at", sa.DateTime(), nullable=False, comment="更新时间"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("email"),
        sa.UniqueConstraint("username"),
    )
    op.create_index("idx_email", "users", ["email"], unique=False)
    op.create_index("idx_is_active", "users", ["is_active"], unique=False)
    op.create_index("idx_username", "users", ["username"], unique=False)
    op.alter_column(
        "vector_documents",
        "id",
        existing_type=sa.INTEGER(),
        comment="主键ID",
        existing_nullable=False,
        autoincrement=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """回滚数据库结构，删除用户表并移除字段注释。

    删除 users 表及其索引，并移除 vector_documents 表的字段注释。
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "vector_documents",
        "id",
        existing_type=sa.INTEGER(),
        comment=None,
        existing_comment="主键ID",
        existing_nullable=False,
        autoincrement=True,
    )
    op.drop_index("idx_username", table_name="users")
    op.drop_index("idx_is_active", table_name="users")
    op.drop_index("idx_email", table_name="users")
    op.drop_table("users")
    # ### end Alembic commands ###
