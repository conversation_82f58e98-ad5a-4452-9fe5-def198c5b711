"""添加新字段

Revision ID: 099147f53a17
Revises:
Create Date: 2025-08-02 16:30:32.893825

"""
import sqlalchemy as sa
from pgvector.sqlalchemy import VECTOR

from alembic import op  # type: ignore[attr-defined]

# revision identifiers, used by Alembic.
revision = "099147f53a17"
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    """升级数据库结构，添加向量文档表。

    创建 vector_documents 表，包含文档内容、向量嵌入、来源、类型等字段，
    并创建相应的索引以优化查询性能。
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "vector_documents",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("content", sa.Text(), nullable=False, comment="文档内容"),
        sa.Column("embedding", VECTOR(dim=1024), nullable=False, comment="文档的向量表示"),
        sa.Column("source", sa.String(length=255), nullable=False, comment="文档来源"),
        sa.Column(
            "document_type", sa.String(length=100), nullable=False, comment="文档类型"
        ),
        sa.Column("created_at", sa.DateTime(), nullable=False, comment="创建时间"),
        sa.Column("updated_at", sa.DateTime(), nullable=False, comment="更新时间"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index("idx_created_at", "vector_documents", ["created_at"], unique=False)
    op.create_index(
        "idx_document_type", "vector_documents", ["document_type"], unique=False
    )
    op.create_index(
        "idx_embedding_hnsw",
        "vector_documents",
        ["embedding"],
        unique=False,
        postgresql_using="hnsw",
        postgresql_with={"m": 16, "ef_construction": 64},
        postgresql_ops={"embedding": "vector_cosine_ops"},
    )
    op.create_index("idx_source", "vector_documents", ["source"], unique=False)
    op.create_index(
        "idx_source_type", "vector_documents", ["source", "document_type"], unique=False
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """回滚数据库结构，删除向量文档表。

    删除 vector_documents 表及其相关的索引。
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("idx_source_type", table_name="vector_documents")
    op.drop_index("idx_source", table_name="vector_documents")
    op.drop_index(
        "idx_embedding_hnsw",
        table_name="vector_documents",
        postgresql_using="hnsw",
        postgresql_with={"m": 16, "ef_construction": 64},
        postgresql_ops={"embedding": "vector_cosine_ops"},
    )
    op.drop_index("idx_document_type", table_name="vector_documents")
    op.drop_index("idx_created_at", table_name="vector_documents")
    op.drop_table("vector_documents")
    # ### end Alembic commands ###
