"""用户表添加字段

Revision ID: f50199286e2b
Revises: 6e72418bc522
Create Date: 2025-08-04 17:06:52.736291

"""
import sqlalchemy as sa

from alembic import op  # type: ignore[attr-defined]

# revision identifiers, used by Alembic.
revision = "f50199286e2b"
down_revision = "6e72418bc522"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "users",
        sa.Column("education", sa.String(length=50), nullable=False, comment="文化程度"),
    )
    op.add_column("users", sa.Column("age", sa.Integer(), nullable=False, comment="年龄"))
    op.add_column(
        "users", sa.Column("gender", sa.String(length=10), nullable=False, comment="性别")
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("users", "gender")
    op.drop_column("users", "age")
    op.drop_column("users", "education")
    # ### end Alembic commands ###
