"""Alembic环境配置

此文件由Alembic自动生成，用于配置数据库迁移环境。
"""

import asyncio
import os

# 导入项目配置和模型
import sys
from logging.config import fileConfig

from sqlalchemy import pool
from sqlalchemy.engine import Connection
from sqlalchemy.ext.asyncio import async_engine_from_config

from alembic import context  # type: ignore[attr-defined]

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.config import config  # noqa: E402
from database.models import Base  # noqa: E402

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config_obj = context.config

# 设置数据库URL
config_obj.set_main_option("sqlalchemy.url", config.database.connection_string)

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config_obj.config_file_name is not None:
    fileConfig(config_obj.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
target_metadata = Base.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config_obj.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config_obj.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def do_run_migrations(connection: Connection) -> None:
    """运行数据库迁移。

    Args:
        connection: 数据库连接对象
    """
    context.configure(connection=connection, target_metadata=target_metadata)

    with context.begin_transaction():
        context.run_migrations()


async def run_async_migrations() -> None:
    """运行异步数据库迁移。

    在此场景中，我们需要创建一个引擎并将连接与上下文关联。
    """

    connectable = async_engine_from_config(
        config_obj.get_section(config_obj.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    async with connectable.connect() as connection:
        await connection.run_sync(do_run_migrations)

    await connectable.dispose()


def run_migrations_online() -> None:
    """运行在线模式下的数据库迁移。

    在此场景中，我们需要创建一个引擎并将连接与上下文关联。
    """

    asyncio.run(run_async_migrations())


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
