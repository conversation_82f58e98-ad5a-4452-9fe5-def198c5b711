#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/8/5 15:00
# <AUTHOR> laogao
# @Email   : <EMAIL>
# @File    : streamlit_app.py
# @Update  : 2025/8/5 15:00 新增Streamlit界面

"""
Streamlit前端应用程序

提供基于Streamlit的Web界面，用于展示和操作LangGraph数学教辅智能体工作流。
支持多种输入类型（文本、图片、LaTeX），实时显示工作流执行进度，
并提供清晰的结果展示和错误处理。
"""

import io
import logging
import os
import time
from typing import Dict, Generator, List, Optional, Tuple

import streamlit as st
from PIL import Image

# 导入工作流相关模块
from workflow.demo import app as math_tutor_app, detect_input_type, create_inputs_with_auto_detection


class StreamlitMathTutorInterface:
    """Streamlit数学教辅智能体界面类"""

    def __init__(self):
        """初始化Streamlit界面"""
        self.app = math_tutor_app
        self.setup_page_config()
        self.setup_session_state()

    def setup_page_config(self) -> None:
        """配置Streamlit页面设置"""
        st.set_page_config(
            page_title="AI数学教辅智能体",
            page_icon="🧮",
            layout="wide",
            initial_sidebar_state="expanded",
            menu_items={
                'Get Help': 'https://github.com/your-repo/langgraph-demo',
                'Report a bug': 'https://github.com/your-repo/langgraph-demo/issues',
                'About': "基于LangGraph的AI数学教辅智能体 - 提供智能数学问题解答和详细解释"
            }
        )

        # 加载自定义CSS
        self.load_custom_css()

    def load_custom_css(self) -> None:
        """加载自定义CSS样式"""
        try:
            css_file = "static/style.css"
            if os.path.exists(css_file):
                with open(css_file, "r", encoding="utf-8") as f:
                    css_content = f.read()
                st.markdown(f"<style>{css_content}</style>", unsafe_allow_html=True)
        except Exception as e:
            # 如果CSS文件不存在或加载失败，使用内联样式
            st.markdown("""
            <style>
            .main .block-container { padding-top: 2rem; max-width: 1200px; }
            .stButton > button { border-radius: 0.5rem; transition: all 0.3s ease; }
            .stButton > button:hover { transform: translateY(-2px); }
            </style>
            """, unsafe_allow_html=True)

    def setup_session_state(self) -> None:
        """初始化会话状态"""
        if 'workflow_history' not in st.session_state:
            st.session_state.workflow_history = []
        if 'current_execution' not in st.session_state:
            st.session_state.current_execution = None
        if 'execution_steps' not in st.session_state:
            st.session_state.execution_steps = []

    def render_header(self) -> None:
        """渲染页面头部"""
        st.title("🧮 AI数学教辅智能体")
        st.markdown("""
        <div style="background-color: #f0f2f6; padding: 1rem; border-radius: 0.5rem; margin-bottom: 2rem;">
            <h4 style="color: #1f77b4; margin: 0;">🚀 智能数学问题解答系统</h4>
            <p style="margin: 0.5rem 0 0 0; color: #666;">
                基于LangGraph构建的多智能体协作系统，提供准确的数学问题解答和详细的解题步骤解释。
                支持文本、图片和LaTeX格式输入，具备自动审计和质量保证机制。
            </p>
        </div>
        """, unsafe_allow_html=True)

    def render_sidebar(self) -> Tuple[str, str, int, str]:
        """渲染侧边栏配置面板"""
        with st.sidebar:
            st.header("⚙️ 配置设置")
            
            # 用户画像设置
            st.subheader("👤 用户画像")
            education_level = st.selectbox(
                "教育水平",
                options=["小学", "初中", "高中", "大学"],
                index=1,  # 默认选择"初中"
                help="选择学生的教育水平，系统会据此调整解答的难度和详细程度"
            )
            
            age = st.slider(
                "年龄",
                min_value=6,
                max_value=25,
                value=14,
                help="学生年龄，用于个性化解答内容"
            )
            
            # 输入类型设置
            st.subheader("📝 输入设置")
            input_mode = st.radio(
                "输入模式",
                options=["自动检测", "手动指定"],
                index=0,
                help="选择输入类型的检测方式"
            )
            
            input_type = "auto"
            if input_mode == "手动指定":
                input_type = st.selectbox(
                    "输入类型",
                    options=["text", "image", "latex"],
                    index=0,
                    format_func=lambda x: {"text": "文本", "image": "图片", "latex": "LaTeX"}[x]
                )
            
            # 系统信息
            st.subheader("ℹ️ 系统信息")
            st.info(f"""
            **当前配置:**
            - 教育水平: {education_level}
            - 年龄: {age}岁
            - 输入模式: {input_mode}
            """)
            
            # 历史记录控制
            if st.button("🗑️ 清空历史记录", type="secondary"):
                st.session_state.workflow_history = []
                st.rerun()
        
        return education_level, input_type, age, input_mode

    def render_input_section(self, input_type: str, input_mode: str) -> Optional[str]:
        """渲染输入区域"""
        st.header("📝 问题输入")
        
        input_content = None
        
        # 创建输入标签页
        if input_mode == "自动检测":
            tab1, tab2, tab3 = st.tabs(["📝 文本输入", "🖼️ 图片上传", "🔢 LaTeX公式"])
        else:
            if input_type == "text":
                tab1, tab2, tab3 = st.tabs(["📝 文本输入", "🖼️ 图片上传 (禁用)", "🔢 LaTeX公式 (禁用)"])
            elif input_type == "image":
                tab1, tab2, tab3 = st.tabs(["📝 文本输入 (禁用)", "🖼️ 图片上传", "🔢 LaTeX公式 (禁用)"])
            else:  # latex
                tab1, tab2, tab3 = st.tabs(["📝 文本输入 (禁用)", "🖼️ 图片上传 (禁用)", "🔢 LaTeX公式"])
        
        with tab1:
            if input_mode == "自动检测" or input_type == "text":
                st.markdown("**输入数学问题文本：**")
                text_input = st.text_area(
                    "数学问题",
                    placeholder="例如：求解方程 2x + 5 = 13\n或：计算函数 f(x) = x² + 3x + 2 的导数",
                    height=120,
                    label_visibility="collapsed"
                )
                if text_input.strip():
                    input_content = text_input.strip()
            else:
                st.warning("当前输入模式不支持文本输入")
        
        with tab2:
            if input_mode == "自动检测" or input_type == "image":
                st.markdown("**上传包含数学问题的图片：**")
                uploaded_file = st.file_uploader(
                    "选择图片文件",
                    type=['png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff', 'webp'],
                    label_visibility="collapsed"
                )
                if uploaded_file is not None:
                    # 显示上传的图片
                    image = Image.open(uploaded_file)
                    st.image(image, caption="上传的图片", use_column_width=True)
                    
                    # 保存图片到临时文件
                    temp_path = f"temp_{uploaded_file.name}"
                    with open(temp_path, "wb") as f:
                        f.write(uploaded_file.getbuffer())
                    input_content = temp_path
            else:
                st.warning("当前输入模式不支持图片上传")
        
        with tab3:
            if input_mode == "自动检测" or input_type == "latex":
                st.markdown("**输入LaTeX数学公式：**")
                latex_input = st.text_area(
                    "LaTeX公式",
                    placeholder="例如：\\frac{d}{dx}(x^2 + 3x + 2)\n或：\\int x^2 dx",
                    height=120,
                    label_visibility="collapsed"
                )
                if latex_input.strip():
                    input_content = latex_input.strip()
                    # 显示LaTeX预览
                    try:
                        st.markdown("**公式预览：**")
                        st.latex(latex_input)
                    except:
                        st.warning("LaTeX公式格式可能有误，但系统仍会尝试处理")
            else:
                st.warning("当前输入模式不支持LaTeX输入")
        
        return input_content

    def render_examples_section(self) -> None:
        """渲染示例区域"""
        with st.expander("📋 查看使用示例", expanded=False):
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.markdown("**📝 文本示例**")
                st.code("""
求解方程 2x + 5 = 13

计算函数 f(x) = x² + 3x + 2 的导数

解不等式 3x - 7 > 8

求 lim(x→0) sin(x)/x
                """)
            
            with col2:
                st.markdown("**🔢 LaTeX示例**")
                st.code(r"""
\frac{d}{dx}(x^2 + 3x + 2)

\int x^2 dx

\lim_{x \to 0} \frac{\sin x}{x}

\sum_{n=1}^{\infty} \frac{1}{n^2}
                """)
            
            with col3:
                st.markdown("**🖼️ 图片要求**")
                st.markdown("""
                - 支持格式：PNG, JPG, JPEG, GIF, BMP, TIFF, WEBP
                - 图片清晰，文字可读
                - 建议分辨率：至少300x300像素
                - 文件大小：建议小于10MB
                """)

    def execute_workflow_with_progress(self, inputs: Dict) -> Generator[Dict, None, None]:
        """执行工作流并显示进度"""
        try:
            # 初始化进度跟踪
            progress_bar = st.progress(0)
            status_text = st.empty()
            step_container = st.container()
            
            steps = []
            step_count = 0
            total_steps = 3  # recognizer, solver, auditor
            
            # 执行工作流
            for state in self.app.stream(inputs, stream_mode="updates"):
                current_node = list(state.keys())[0] if state else ""
                
                # 更新进度
                if current_node == "recognizer":
                    step_count = 1
                    status_text.text("🔍 步骤1/3: 问题识别中...")
                    steps.append({"node": "recognizer", "status": "running", "message": "正在分析问题类型和内容..."})
                elif current_node == "solver":
                    step_count = 2
                    status_text.text("🧮 步骤2/3: 生成解答中...")
                    steps.append({"node": "solver", "status": "running", "message": "正在生成详细解答和解释..."})
                elif current_node == "auditor":
                    step_count = 3
                    status_text.text("✅ 步骤3/3: 质量审计中...")
                    steps.append({"node": "auditor", "status": "running", "message": "正在验证答案准确性..."})
                
                progress_bar.progress(step_count / total_steps)
                
                # 显示步骤状态
                with step_container:
                    for i, step in enumerate(steps):
                        if step["status"] == "running":
                            st.info(f"🔄 {step['message']}")
                        else:
                            st.success(f"✅ {step['message']}")
                
                yield state
            
            # 完成
            progress_bar.progress(1.0)
            status_text.text("✅ 工作流执行完成！")
            
        except Exception as e:
            st.error(f"工作流执行失败: {str(e)}")
            yield {}

    def render_results_section(self, final_state: Dict) -> None:
        """渲染结果展示区域"""
        st.header("📊 解答结果")

        if not final_state:
            st.warning("暂无结果")
            return

        # 获取最终状态
        last_node_state = final_state.get(list(final_state.keys())[-1], {})

        # 显示问题识别结果
        if "problem_text" in last_node_state:
            with st.expander("🔍 问题识别结果", expanded=True):
                st.markdown("**识别出的问题文本：**")
                st.code(last_node_state["problem_text"], language="text")

        # 显示最终解答
        if "final_response" in last_node_state:
            with st.expander("📝 最终解答", expanded=True):
                st.markdown("**经过审计的完整解答：**")
                st.markdown(last_node_state["final_response"])

        # 显示审计信息
        if "audit_critique" in last_node_state and last_node_state["audit_critique"]:
            with st.expander("⚠️ 审计意见", expanded=False):
                st.warning("**审计员发现的问题：**")
                st.markdown(last_node_state["audit_critique"])

        # 显示重试信息
        retry_count = last_node_state.get("retry_count", 0)
        if retry_count > 1:
            st.info(f"🔄 系统进行了 {retry_count - 1} 次重试以确保答案准确性")

    def render_workflow_history(self) -> None:
        """渲染工作流历史记录"""
        if not st.session_state.workflow_history:
            return

        st.header("📚 历史记录")

        for i, record in enumerate(reversed(st.session_state.workflow_history)):
            with st.expander(f"记录 {len(st.session_state.workflow_history) - i}: {record['timestamp']}", expanded=False):
                col1, col2 = st.columns([1, 2])

                with col1:
                    st.markdown("**输入信息：**")
                    st.json({
                        "问题": record["input_content"][:100] + "..." if len(record["input_content"]) > 100 else record["input_content"],
                        "类型": record["input_type"],
                        "教育水平": record["user_profile"]["education"],
                        "年龄": record["user_profile"]["age"]
                    })

                with col2:
                    st.markdown("**解答结果：**")
                    if record.get("final_response"):
                        st.markdown(record["final_response"][:300] + "..." if len(record["final_response"]) > 300 else record["final_response"])
                    else:
                        st.warning("解答失败或未完成")

    def save_to_history(self, inputs: Dict, final_state: Dict) -> None:
        """保存执行记录到历史"""
        import datetime

        last_node_state = final_state.get(list(final_state.keys())[-1], {}) if final_state else {}

        record = {
            "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "input_content": inputs.get("input_content", ""),
            "input_type": inputs.get("input_type", ""),
            "user_profile": inputs.get("user_profile", {}),
            "final_response": last_node_state.get("final_response", ""),
            "retry_count": last_node_state.get("retry_count", 0)
        }

        st.session_state.workflow_history.append(record)

        # 限制历史记录数量
        if len(st.session_state.workflow_history) > 10:
            st.session_state.workflow_history = st.session_state.workflow_history[-10:]

    def run(self) -> None:
        """运行Streamlit应用"""
        # 渲染页面组件
        self.render_header()

        # 获取侧边栏配置
        education_level, input_type, age, input_mode = self.render_sidebar()

        # 主要内容区域
        col1, col2 = st.columns([2, 1])

        with col1:
            # 输入区域
            input_content = self.render_input_section(input_type, input_mode)

            # 示例区域
            self.render_examples_section()

            # 执行按钮
            if st.button("🚀 开始解答", type="primary", use_container_width=True):
                if not input_content:
                    st.error("请先输入数学问题！")
                else:
                    # 准备输入数据
                    if input_mode == "自动检测":
                        detected_type = detect_input_type(input_content)
                        inputs = create_inputs_with_auto_detection(
                            input_content,
                            {"education": education_level, "age": age}
                        )
                    else:
                        inputs = {
                            "input_content": input_content,
                            "input_type": input_type,
                            "user_profile": {"education": education_level, "age": age},
                            "retry_count": 0,
                        }

                    # 显示输入信息
                    st.info(f"📝 输入类型: {inputs['input_type']} | 教育水平: {education_level} | 年龄: {age}岁")

                    # 执行工作流
                    with st.spinner("正在处理您的数学问题..."):
                        final_state = None
                        for state in self.execute_workflow_with_progress(inputs):
                            final_state = state

                        # 显示结果
                        if final_state:
                            self.render_results_section(final_state)
                            self.save_to_history(inputs, final_state)
                        else:
                            st.error("工作流执行失败，请检查输入或稍后重试")

        with col2:
            # 历史记录
            self.render_workflow_history()


def main() -> None:
    """主函数"""
    try:
        # 创建并运行Streamlit应用
        app = StreamlitMathTutorInterface()
        app.run()

    except Exception as e:
        st.error(f"应用启动失败: {str(e)}")
        st.exception(e)


if __name__ == "__main__":
    main()
