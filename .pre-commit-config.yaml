# pre-commit 配置文件
# 用于在提交代码前自动运行代码格式化和质量检查

# 全局配置
exclude: '^$'
fail_fast: false
minimum_pre_commit_version: '3.0.0'

# 默认安装的钩子类型
default_install_hook_types: [pre-commit, pre-push]

# 默认语言版本
default_language_version:
    python: python3.12

# 仓库配置
repos:
    # 基础代码质量检查钩子
    - repo: https://github.com/pre-commit/pre-commit-hooks
      rev: v4.5.0
      hooks:
          # 检查 YAML 文件语法
          - id: check-yaml
            name: 检查 YAML 语法
            description: 检查 YAML 文件的语法正确性

          # 检查 JSON 文件语法
          - id: check-json
            name: 检查 JSON 语法
            description: 检查 JSON 文件的语法正确性

          # 检查合并冲突标记
          - id: check-merge-conflict
            name: 检查合并冲突
            description: 检查文件中是否存在未解决的合并冲突标记

          # 检查文件末尾换行符
          - id: end-of-file-fixer
            name: 修复文件末尾
            description: 确保文件以换行符结尾

          # 修复尾随空格
          - id: trailing-whitespace
            name: 修复尾随空格
            description: 移除行尾的空白字符

          # 检查大文件
          - id: check-added-large-files
            name: 检查大文件
            description: 防止提交过大的文件
            args: ['--maxkb=500']

          # 检查可执行文件
          - id: check-executables-have-shebangs
            name: 检查可执行文件
            description: 确保可执行文件有正确的 shebang

          # 检查私钥文件
          - id: detect-private-key
            name: 检测私钥
            description: 防止意外提交私钥文件

    # Python 代码格式化 - Black
    - repo: https://github.com/psf/black
      rev: 23.12.1
      hooks:
          - id: black
            name: Black 代码格式化
            description: 使用 Black 自动格式化 Python 代码
            language_version: python3.12
            args: ['--line-length=88', '--target-version=py312']

    # Python 导入排序 - isort
    - repo: https://github.com/pycqa/isort
      rev: 5.13.2
      hooks:
          - id: isort
            name: isort 导入排序
            description: 自动排序和格式化 Python 导入语句
            args: ['--profile=black', '--line-length=88']

    # Python 代码质量检查 - flake8
    - repo: https://github.com/pycqa/flake8
      rev: 7.0.0
      hooks:
          - id: flake8
            name: flake8 代码检查
            description: 检查 Python 代码风格和潜在问题
            args: [
                '--max-line-length=88',
                '--extend-ignore=E203,W503',
                '--per-file-ignores=__init__.py:F401'
            ]

    # Python 类型检查 - mypy
    - repo: https://github.com/pre-commit/mirrors-mypy
      rev: v1.8.0
      hooks:
          - id: mypy
            name: mypy 类型检查
            description: 检查 Python 代码的类型注解
            # 配置在 pyproject.toml 中，这里使用默认配置

    # 安全漏洞检查 - bandit
    - repo: https://github.com/PyCQA/bandit
      rev: 1.7.5
      hooks:
          - id: bandit
            name: bandit 安全检查
            description: 检查 Python 代码中的安全漏洞
            args: ['-f', 'json', '-o', 'bandit-report.json']
            exclude: 'tests/'

    # 文档字符串检查 - pydocstyle
    - repo: https://github.com/pycqa/pydocstyle
      rev: 6.3.0
      hooks:
          - id: pydocstyle
            name: pydocstyle 文档检查
            description: 检查 Python 文档字符串的格式和内容
            args: ['--convention=google', '--add-ignore=D100,D104,D106,D200,D205,D400,D401,D403,D415,D212,D103,D107,D202']

    # 本地钩子 - 项目特定的检查
    - repo: local
      hooks:
          # 检查版本号更新
          - id: check-version-update
            name: 检查版本号更新
            description: 检查 CHANGELOG.md 和 pyproject.toml 中的版本号是否一致
            entry: python scripts/check_version.py
            language: python
            files: '^(CHANGELOG\.md|pyproject\.toml)$'
            pass_filenames: false
            always_run: true

          # 检查中文注释格式
          - id: check-chinese-comments
            name: 检查中文注释格式
            description: 确保中文注释格式正确
            entry: python scripts/check_chinese_comments.py
            language: python
            types: [python]
            exclude: '^(tests/|examples/)'

          # 运行测试套件
          - id: run-tests
            name: 运行测试
            description: 运行项目的测试套件
            entry: uv run python run_tests.py
            language: system
            types: [python]
            pass_filenames: false
            always_run: true
            stages: [pre-push]  # 仅在推送前运行测试
